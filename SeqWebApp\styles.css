/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Color Palette - Music Production Theme */
    --primary-bg: #0a0a0f;
    --secondary-bg: #1a1a2e;
    --tertiary-bg: #16213e;
    --accent-bg: #0f3460;

    /* Accent Colors */
    --primary-accent: #00d4ff;
    --secondary-accent: #ff6b6b;
    --success-accent: #4ecdc4;
    --warning-accent: #feca57;
    --danger-accent: #ff4757;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #7a8ca8;
    --text-accent: var(--primary-accent);

    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.12);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--tertiary-bg) 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: auto;
    line-height: 1.6;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-md);
    position: relative;
}

/* Compact Header */
header {
    text-align: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm) 0;
    position: relative;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-accent) 20%,
        var(--secondary-accent) 50%,
        var(--success-accent) 80%,
        transparent 100%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

header h1 {
    font-size: clamp(1.2rem, 3vw, 1.6rem);
    font-weight: 600;
    color: var(--text-accent);
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    letter-spacing: -0.01em;
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 300;
    opacity: 0.8;
    margin: 0;
}

/* Compact Controls */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--glass-bg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.03),
        transparent);
    transition: left var(--transition-slow);
}

.controls:hover::before {
    left: 100%;
}

.transport-controls {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid transparent;
    font-family: inherit;
    user-select: none;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: translateY(1px) scale(0.98);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    color: var(--text-primary);
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
    border-color: rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.4);
    border-color: rgba(0, 212, 255, 0.5);
}

.btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--tertiary-bg), var(--accent-bg));
    color: var(--text-primary);
    border-color: var(--glass-border);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--accent-bg), var(--tertiary-bg));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-preset {
    background: linear-gradient(135deg, var(--secondary-accent), var(--danger-accent));
    color: var(--text-primary);
    margin: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 0.85rem;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
    border-color: rgba(255, 107, 107, 0.3);
}

.btn-preset:hover {
    background: linear-gradient(135deg, var(--danger-accent), var(--secondary-accent));
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

/* Compact Tempo control */
.tempo-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: var(--glass-bg);
    border-radius: var(--radius-md);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.tempo-control label {
    font-weight: 600;
    color: var(--text-accent);
    font-size: 0.85rem;
    text-align: center;
}

/* Sliders - Tempo slider remains larger, param sliders are compact */
.slider {
    width: 160px;
    height: 8px;
    border-radius: var(--radius-sm);
    background: linear-gradient(90deg, var(--tertiary-bg), var(--accent-bg));
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    position: relative;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 1px solid var(--glass-border);
}

.param-slider {
    height: 4px;
    border-radius: 2px;
    background: linear-gradient(90deg, var(--tertiary-bg), var(--accent-bg));
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    position: relative;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.slider:hover {
    background: linear-gradient(90deg, var(--accent-bg), var(--tertiary-bg));
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
}

.param-slider:hover {
    background: linear-gradient(90deg, var(--accent-bg), var(--tertiary-bg));
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.15);
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    cursor: pointer;
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5), 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-fast);
}

.param-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    cursor: pointer;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.4), 0 1px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-fast);
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.7), 0 4px 12px rgba(0, 0, 0, 0.4);
}

.param-slider::-webkit-slider-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(0, 212, 255, 0.6), 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5), 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all var(--transition-fast);
}

.param-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-accent), var(--success-accent));
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.4), 0 1px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.7), 0 4px 12px rgba(0, 0, 0, 0.4);
}

.param-slider::-moz-range-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(0, 212, 255, 0.6), 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Compact Sequencer info - 1/8 original size */
.sequencer-info {
    margin-bottom: var(--spacing-md);
}

.info-panel {
    background: var(--glass-bg);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
    max-width: 300px;
    margin: 0 auto;
}

.info-panel:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.info-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--primary-accent) 50%,
        transparent 100%);
    opacity: 0.4;
}

.info-panel h3 {
    color: var(--text-accent);
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: -0.01em;
    text-align: center;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    padding: 2px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: all var(--transition-fast);
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 2px;
    padding-left: 2px;
    padding-right: 2px;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-item span:first-child {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.75rem;
}

.info-item span:last-child {
    color: var(--text-accent);
    font-weight: 700;
    font-size: 0.8rem;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.2);
}

/* Compact Parameter controls - Single vertical column */
.parameter-controls {
    margin-bottom: var(--spacing-lg);
}

.parameter-controls h3 {
    color: var(--text-accent);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    letter-spacing: -0.01em;
}

.param-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-md);
    background: var(--glass-bg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    max-width: 320px;
    margin: 0 auto;
}

.param-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        var(--secondary-accent) 0%,
        var(--primary-accent) 25%,
        var(--success-accent) 50%,
        var(--warning-accent) 75%,
        var(--secondary-accent) 100%);
    opacity: 0.5;
    animation: colorShift 4s ease-in-out infinite;
}

@keyframes colorShift {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

.param-control {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.04);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    height: 2.5rem; /* Compact height - approximately 4 line breaks equivalent */
    min-height: 2.5rem;
    max-height: 2.5rem;
}

.param-control:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.param-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.03),
        transparent);
    transition: left var(--transition-normal);
}

.param-control:hover::before {
    left: 100%;
}

.param-control label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.75rem;
    letter-spacing: 0.01em;
    min-width: 80px;
    text-align: left;
    white-space: nowrap;
    flex-shrink: 0;
}

.param-control .param-slider {
    flex: 1;
    height: 4px;
    margin: 0 var(--spacing-xs);
}

/* Compact P5.js container */
#p5-container {
    display: flex;
    justify-content: center;
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-md);
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--radius-lg);
    border: 2px solid rgba(0, 212, 255, 0.3);
    backdrop-filter: blur(12px);
    box-shadow:
        inset 0 1px 0 rgba(255, 255, 255, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

#p5-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center,
        rgba(0, 212, 255, 0.03) 0%,
        transparent 70%);
    pointer-events: none;
}

/* Compact Pattern presets */
.pattern-presets {
    text-align: center;
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--glass-bg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.pattern-presets::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--secondary-accent) 20%,
        var(--primary-accent) 50%,
        var(--success-accent) 80%,
        transparent 100%);
    opacity: 0.4;
}

.pattern-presets h3 {
    color: var(--text-accent);
    margin-bottom: var(--spacing-sm);
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.preset-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Focus States */
.btn:focus-visible,
.slider:focus-visible,
.param-slider:focus-visible {
    outline: 2px solid var(--primary-accent);
    outline-offset: 2px;
}

/* Disabled States */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Error States */
.error {
    border-color: var(--danger-accent) !important;
    box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.2) !important;
}

/* Success States */
.success {
    border-color: var(--success-accent) !important;
    box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2) !important;
}

/* Responsive design for compact layout */
@media (max-width: 768px) {
    .container {
        padding: var(--spacing-sm);
    }

    header {
        padding: var(--spacing-xs) 0;
        margin-bottom: var(--spacing-sm);
    }

    header h1 {
        font-size: clamp(1rem, 3vw, 1.4rem);
    }

    header p {
        font-size: 0.7rem;
    }

    .controls {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }

    .transport-controls {
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .param-grid {
        max-width: 100%;
        padding: var(--spacing-sm);
    }

    .param-control {
        height: 2.2rem;
        min-height: 2.2rem;
        max-height: 2.2rem;
        padding: var(--spacing-xs);
    }

    .param-control label {
        font-size: 0.7rem;
        min-width: 70px;
    }

    .info-panel {
        max-width: 100%;
        padding: var(--spacing-xs);
    }

    .info-panel h3 {
        font-size: 0.8rem;
    }

    .info-item span:first-child {
        font-size: 0.7rem;
    }

    .info-item span:last-child {
        font-size: 0.75rem;
    }

    .preset-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .btn {
        min-width: 100px;
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 0.8rem;
    }

    #p5-container {
        padding: var(--spacing-sm);
        margin: var(--spacing-md) 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: var(--spacing-xs);
    }

    .param-control {
        height: 2rem;
        min-height: 2rem;
        max-height: 2rem;
    }

    .param-control label {
        font-size: 0.65rem;
        min-width: 60px;
    }

    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
        min-width: 80px;
    }

    .param-slider {
        height: 3px;
    }

    .param-slider::-webkit-slider-thumb {
        width: 12px;
        height: 12px;
    }

    .param-slider::-moz-range-thumb {
        width: 12px;
        height: 12px;
    }
}

/* Enhanced Animations */
.active {
    animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
    }
}

/* Enhanced Glow effect */
.glow {
    box-shadow:
        0 0 20px rgba(0, 212, 255, 0.6),
        0 0 40px rgba(0, 212, 255, 0.4),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    from {
        box-shadow:
            0 0 20px rgba(0, 212, 255, 0.6),
            0 0 40px rgba(0, 212, 255, 0.4),
            inset 0 0 20px rgba(0, 212, 255, 0.1);
    }
    to {
        box-shadow:
            0 0 30px rgba(0, 212, 255, 0.8),
            0 0 60px rgba(0, 212, 255, 0.6),
            inset 0 0 30px rgba(0, 212, 255, 0.2);
    }
}

/* Fade-in animation for page load */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container > * {
    animation: fadeIn 0.6s ease-out forwards;
}

.container > *:nth-child(1) { animation-delay: 0.1s; }
.container > *:nth-child(2) { animation-delay: 0.2s; }
.container > *:nth-child(3) { animation-delay: 0.3s; }
.container > *:nth-child(4) { animation-delay: 0.4s; }
.container > *:nth-child(5) { animation-delay: 0.5s; }
.container > *:nth-child(6) { animation-delay: 0.6s; }

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--primary-bg);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--primary-accent), var(--success-accent));
    border-radius: var(--radius-sm);
    border: 2px solid var(--primary-bg);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--success-accent), var(--primary-accent));
}

/* Selection styling */
::selection {
    background: rgba(0, 212, 255, 0.3);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(0, 212, 255, 0.3);
    color: var(--text-primary);
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}