/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: auto;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid #444;
}

header h1 {
    font-size: 2.5rem;
    color: #00d4ff;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

header p {
    font-size: 1.1rem;
    color: #aaa;
}

/* Controls */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.transport-controls {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #666, #888);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #777, #999);
    transform: translateY(-1px);
}

.btn-preset {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
    margin: 5px;
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-preset:hover {
    background: linear-gradient(45deg, #ff7979, #fd6c6c);
    transform: translateY(-1px);
}

/* Tempo control */
.tempo-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.tempo-control label {
    font-weight: 600;
    color: #00d4ff;
}

/* Sliders */
.slider, .param-slider {
    width: 150px;
    height: 6px;
    border-radius: 3px;
    background: #333;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb, .param-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #00d4ff;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.slider::-moz-range-thumb, .param-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #00d4ff;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Sequencer info */
.sequencer-info {
    margin-bottom: 30px;
}

.info-panel {
    background: rgba(255, 255, 255, 0.05);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.info-panel h3 {
    color: #00d4ff;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-item span:first-child {
    color: #aaa;
}

.info-item span:last-child {
    color: #00d4ff;
    font-weight: 600;
}

/* Parameter controls */
.parameter-controls {
    margin-bottom: 30px;
}

.parameter-controls h3 {
    color: #00d4ff;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.param-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.param-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.param-control label {
    font-weight: 600;
    color: #fff;
    text-align: center;
}

.param-control .param-slider {
    width: 100%;
}

/* P5.js container */
#p5-container {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 2px solid rgba(0, 212, 255, 0.3);
}

/* Pattern presets */
.pattern-presets {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.pattern-presets h3 {
    color: #00d4ff;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.preset-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        gap: 20px;
    }
    
    .param-grid {
        grid-template-columns: 1fr;
    }
    
    .preset-buttons {
        flex-direction: column;
        align-items: center;
    }
}

/* Animation for active elements */
.active {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Glow effect for important elements */
.glow {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e2e;
}

::-webkit-scrollbar-thumb {
    background: #00d4ff;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0099cc;
}