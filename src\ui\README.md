# UI Module

## Functionality


\

## Touch Buttons (MPR121 @ 0x5D)
uses touchButtonHandler.cpp
- **Mode Switching**  
    *Simultaneously press any button from 0–5 (Seq1) and any from 6–11 (Seq2) to toggle between Parameter Mode and Control Mode.*


#### Parameter Mode

- **Seq1 Parameter Buttons (0–5)**  
    *Hold to record parameter values for Seq1:*
    - 0: Note
    - 1: Velocity
    - 2: Filter
    - 3: Attack
    - 4: Decay
    - 5: Octave

- **Seq2 Parameter Buttons (6–11)**  
    *Hold to record parameter values for Seq2:*
    - 6: Note
    - 7: Velocity
    - 8: Filter
    - 9: Attack
    - 10: Decay
    - 11: Octave

---

#### Control Mode

- **Control Buttons (0–11)**  
    *Mapped to global and advanced functions:*

    | Button | Function                                                                 |
    |--------|--------------------------------------------------------------------------|
    | 0      | Play/Stop toggle                                                         |
    | 1      | Cycle through scales                                                     |
    | 2      | Cycle through themes                                                     |
    | 3      | Select AS5600 for Tempo control                                          |
    | 4      | Select AS5600 for Shuffle control                                        |
    | 5      | Select AS5600 for Delay Time control                                     |
    | 6      | Select AS5600 for Delay Feedback control                                 |
    | 7      | Toggle Delay On/Off                                                      |
    | 8      | LFO1 Assign Mode for Seq1                                                |
    | 9      | LFO2 Assign Mode for Seq2                                                |
    | 10     | Press: Randomize Seq1<br>Hold: Reset Seq1                                |
    | 11     | Press: Randomize Seq2<br>Hold: Reset Seq2                                |

- *Buttons 10 and 11 support both short press (randomize) and long press (reset) actions.*
- *AS5600 selection buttons (3–6) determine which parameter the encoder will control.*
- *All control buttons are handled centrally for consistent event processing.*







---


### External Dependencies

The UI module requires access to these external components:

- **Sequencer Objects**: `seq1`, `seq2`
- **AS5600 System**: `currentAS5600Parameter`, base values, sensor instance
- **Global State**: `currentScale`, `currentThemeIndex`, `isClockRunning`
- **External Functions**: `updateParametersForStep()`, `onClockStart()`, `onClockStop()`



## Constants

### Button Indices
```cpp
// Touch Buttons (MPR121 @ 0x5D) - Control Mode
constexpr uint8_t BUTTON_PLAY_STOP = 0;
constexpr uint8_t BUTTON_CHANGE_SCALE = 1;
constexpr uint8_t BUTTON_CHANGE_THEME = 2;
constexpr uint8_t BUTTON_SELECT_TEMPO = 3;
constexpr uint8_t BUTTON_SELECT_SHUFFLE = 4;
constexpr uint8_t BUTTON_SELECT_DELAY_TIME = 5;
constexpr uint8_t BUTTON_SELECT_DELAY_FEEDBACK = 6;
constexpr uint8_t BUTTON_TOGGLE_DELAY = 7;
constexpr uint8_t BUTTON_LFO1_ASSIGN = 8;
constexpr uint8_t BUTTON_LFO2_ASSIGN = 9;
constexpr uint8_t BUTTON_RANDOMIZE_SEQ1 = 10; // short press randomize, long press reset
constexpr uint8_t BUTTON_RANDOMIZE_SEQ2 = 11; // short press randomize, long press reset
```

### Timing
```cpp
constexpr unsigned long LONG_PRESS_THRESHOLD = 400; // ms
constexpr unsigned long AS5600_DOUBLE_PRESS_WINDOW = 500; // ms
constexpr unsigned long CONTROL_LED_FLASH_DURATION_MS = 250;
```

---

## Future Enhancements

- **Gesture Recognition**: Multi-button combinations
- **Customizable Mappings**: Runtime button remapping
- **UI Themes**: Different interaction modes
- **Macro Recording**: Record and playback button sequences

This module provides a solid foundation for UI management while maintaining the real-time performance requirements of the embedded sequencer system.

## See Also

- [Main Project README](../../README.md)
- [Matrix Module](../matrix/README.md)
- [Sensors Module](../sensors/README.md)
