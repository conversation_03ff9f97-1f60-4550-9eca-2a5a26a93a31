#ifndef HARDWARE_CONFIG_H
#define HARDWARE_CONFIG_H

/**
 * @file HardwareConfig.h
 * @brief Centralized hardware configuration for PicoMudras Sequencer
 * 
 * This file contains all hardware pin definitions, buffer sizes, sample rates,
 * and other static configuration values used throughout the project.
 * Centralizing these values makes the codebase more maintainable and easier
 * to port to different hardware configurations.
 */
// =============================================================================
// HARDWARE FEATURE FLAGS
// =============================================================================

// Enable/disable hardware features
#define FEATURE_TOUCH_BUTTONS_ENABLED       1
#define FEATURE_TOUCH_MATRIX_ENABLED        1   // Currently disabled
#define FEATURE_DISTANCE_SENSOR_ENABLED     1
#define FEATURE_AS5600_ENCODER_ENABLED      1
#define FEATURE_LED_MATRIX_ENABLED          1
#define FEATURE_USB_MIDI_ENABLED            1

// =============================================================================
// AUDIO CONFIGURATION
// =============================================================================

// Audio System Parameters
#define AUDIO_SAMPLE_RATE                   48000
#define AUDIO_BUFFER_SAMPLE_LENGTH          576u
#define AUDIO_SILENCE_BUFFER_SAMPLE_LENGTH  256u
#define AUDIO_BUFFERS_PER_CHANNEL           3u
#define AUDIO_MAX_CHANNELS                  2u
#define AUDIO_SAMPLES_PER_BUFFER            256
#define AUDIO_NUM_BUFFERS                   3

// Audio I2S Pin Configuration
#define AUDIO_I2S_DATA_PIN                  15
#define AUDIO_I2S_CLOCK_PIN_BASE            16
#define AUDIO_I2S_PIO                       1
#define AUDIO_I2S_DMA_IRQ                   1

// Audio Processing Constants
#define AUDIO_MAX_DELAY_TIME_SEC            1.8f
#define AUDIO_MAX_DELAY_SAMPLES             ((size_t)(AUDIO_SAMPLE_RATE * AUDIO_MAX_DELAY_TIME_SEC))
#define AUDIO_FREQ_SLEW_RATE                0.0005f
#define AUDIO_FEEDBACK_FADE_RATE            0.0001f
#define AUDIO_OSC_DETUNE_FACTOR             0.0014f
#define AUDIO_INT16_MAX_AS_FLOAT            32767.0f
#define AUDIO_INT16_MIN_AS_FLOAT            -32768.0f

// =============================================================================
// LED MATRIX CONFIGURATION
// =============================================================================

// LED Matrix Dimensions
#define LED_MATRIX_WIDTH                    8
#define LED_MATRIX_HEIGHT                   8
#define LED_MATRIX_TOTAL_LEDS               (LED_MATRIX_WIDTH * LED_MATRIX_HEIGHT)
#define LED_MATRIX_DATA_PIN                 1
#define LED_MATRIX_DEFAULT_BRIGHTNESS       200

// LED Animation Constants
#define LED_SHUFFLE_ANIMATION_START         24
#define LED_SHUFFLE_ANIMATION_END           40
#define LED_VOICE2_OFFSET                   32

// =============================================================================
// TOUCH INTERFACE CONFIGURATION
// =============================================================================

// Touch Matrix Configuration
#define TOUCH_MATRIX_ROWS                   4
#define TOUCH_MATRIX_COLS                   8
#define TOUCH_MATRIX_BUTTON_COUNT           32
#define TOUCH_MATRIX_I2C_ADDRESS           0x5A  
#define TOUCH_MATRIX_IRQ_PIN                9

// Touch Matrix Pin Mappings
#define TOUCH_MATRIX_ROW_PINS               {3, 2, 1, 0}
#define TOUCH_MATRIX_COL_PINS               {4, 5, 6, 7, 8, 9, 10, 11}

// MPR121 Configuration
#define TOUCH_BUTTONS_COUNT           12
#define TOUCH_BUTTONS_IRQ_PIN              10
#define TOUCH_BUTTONS_I2C_ADDRESS           0x5D  

// =============================================================================
// SENSOR CONFIGURATION
// =============================================================================

// Distance Sensor (VL53L1X) Configuration
#define DISTANCE_SENSOR_I2C_SDA             4
#define DISTANCE_SENSOR_I2C_SCL             5
#define DISTANCE_SENSOR_IRQ_PIN             2   // Optional interrupt pin
#define DISTANCE_SENSOR_READ_INTERVAL_MS    20
#define DISTANCE_SENSOR_MAX_HEIGHT          1400
#define DISTANCE_SENSOR_MIN_HEIGHT          74

// AS5600 Magnetic Encoder Configuration
#define AS5600_I2C_ADDRESS                  0x36
#define AS5600_I2C_SDA                      4   // Shared with distance sensor
#define AS5600_I2C_SCL                      5   // Shared with distance sensor

// =============================================================================
// I2C CONFIGURATION
// =============================================================================

// I2C Bus Configuration
#define I2C_SDA_PIN                         4
#define I2C_SCL_PIN                         5
#define I2C_FREQUENCY                       100000  // 100kHz

// =============================================================================
// SEQUENCER CONFIGURATION
// =============================================================================

// Sequencer Timing
#define SEQUENCER_PPQN_PER_STEP             120
#define SEQUENCER_MAX_STEPS                 16
#define SEQUENCER_NUM_VOICES                2
#define SEQUENCER_NUM_SHUFFLE_TEMPLATES     16
#define SEQUENCER_SHUFFLE_TEMPLATE_SIZE     16

// =============================================================================
// MIDI CONFIGURATION
// =============================================================================

// MIDI Settings
#define MIDI_MAX_NOTES                      16
#define MIDI_CHANNEL_1                      1
#define MIDI_CHANNEL_2                      2
#define MIDI_DEFAULT_VELOCITY               90

// =============================================================================
// SYSTEM CONFIGURATION
// =============================================================================

// Core Assignment
#define AUDIO_CORE                          0   // Core 0 handles audio processing
#define UI_CORE                             1   // Core 1 handles UI and control

// Timing Constants
#define UI_UPDATE_INTERVAL_MS               10
#define SENSOR_UPDATE_INTERVAL_MS           20
#define LED_UPDATE_INTERVAL_MS              32  // ~60 FPS

// Memory Configuration
#define USB_DPRAM_SIZE                      4096
#define SPINLOCK_ID_AUDIO_FREE_LIST         6
#define SPINLOCK_ID_AUDIO_PREPARED_LISTS    7

// =============================================================================
// MATHEMATICAL CONSTANTS
// =============================================================================

#define PI_F                                3.1415927410125732421875f
#define TWO_PI_F                            (2.0f * PI_F)
#define HALF_PI_F                           (PI_F / 2.0f)

// =============================================================================
// AUDIO BUFFER FORMATS
// =============================================================================

#define AUDIO_BUFFER_FORMAT_PCM_S16         1   // signed 16bit PCM
#define AUDIO_BUFFER_FORMAT_PCM_S8          2   // signed 8bit PCM
#define AUDIO_BUFFER_FORMAT_PCM_U16         3   // unsigned 16bit PCM
#define AUDIO_BUFFER_FORMAT_PCM_U8          4   // unsigned 8bit PCM


// =============================================================================
// VALIDATION MACROS
// =============================================================================

// Compile-time validation
#if AUDIO_SAMPLE_RATE <= 0
#error "AUDIO_SAMPLE_RATE must be positive"
#endif

#if LED_MATRIX_WIDTH * LED_MATRIX_HEIGHT != LED_MATRIX_TOTAL_LEDS
#error "LED matrix dimensions do not match total LED count"
#endif

#if TOUCH_MATRIX_ROWS * TOUCH_MATRIX_COLS != TOUCH_MATRIX_BUTTON_COUNT
#error "Touch matrix dimensions do not match button count"
#endif

#endif // HARDWARE_CONFIG_H