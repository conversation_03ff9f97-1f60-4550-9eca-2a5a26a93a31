// =======================
//   PICO MUDRAS SEQUENCER
// =======================


#include "src/includes.h"

void setup();
void setup1();
void loop();
void loop1();

// External reference to the application instance for use by other modules
extern PicoMudrasApp app;


void setup() {
    if (!app.initialize()) {
        Serial.println("[ERROR] Application initialization failed!");
        while(1) { delay(1000); } // Halt on initialization failure
    }
    app.setupCore0();
}

void setup1() {
    app.setupCore1();
}

// Core 0 loop - Audio processing
void loop() {
    app.runCore0();
}

// Core 1 loop - UI and control
void loop1() {
    app.runCore1();
}

