#include "UIEventHandler.h"
#include "../sequencer/Sequencer.h"
#include "../midi/MidiManager.h"
#include "ButtonManager.h"

void initUIEventHandler(UIState &UIState)
{
    initButtonManager(UIState);
}

void matrixEventHandler(const MatrixButtonEvent &evt, UIState &UIState, Sequencer &seq1, Sequencer &seq2, MidiNoteManager &midiNoteManager)
{
    if (UIState.touchButtonMode != PARAMETER_MODE)
    {
        return; // Only handle matrix events in parameter mode
    }

    if (evt.type == MATRIX_BUTTON_PRESSED)
    {
        int step = -1;
        Sequencer* activeSeq = nullptr;

        if (evt.buttonIndex >= 0 && evt.buttonIndex <= 15)
        {
            step = evt.buttonIndex;
            activeSeq = &seq1;
        }
        else if (evt.buttonIndex >= 16 && evt.buttonIndex <= 31)
        {
            step = evt.buttonIndex - 16;
            activeSeq = &seq2;
        }

        if (activeSeq != nullptr && step != -1)
        {
            activeSeq->toggleStep(step);
            UIState.selectedStepForEdit = step;
        }
    }
}
