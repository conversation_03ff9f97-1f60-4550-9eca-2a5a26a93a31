// PicoMudras Polymetric Sequencer Visualizer
// Based on the PicoMudrasSequencer project architecture

// Parameter definitions matching the C++ code
const PARAM_NAMES = [
    'Note', 'Velocity', 'Filter', 'Attack', 'Decay', 'Octave', 'Gate Length', 'Gate', 'Slide'
];

const PARAM_COLORS = [
    '#ff6b6b', // Note - Red
    '#4ecdc4', // Velocity - Teal
    '#45b7d1', // Filter - Blue
    '#96ceb4', // Attack - Green
    '#feca57', // Decay - Yellow
    '#ff9ff3', // Octave - Pink
    '#54a0ff', // Gate Length - Light Blue
    '#5f27cd', // Gate - Purple
    '#00d2d3'  // Slide - Cyan
];

const DEFAULT_STEPS = 16;
const MAX_STEPS = 16;
const MIN_STEPS = 1;

// Sequencer state
let sequencer = {
    isPlaying: false,
    currentStep: 0,
    tempo: 70,
    stepInterval: null,
    parameterTracks: [],
    currentStepPerParam: new Array(PARAM_NAMES.length).fill(0)
};

// P5.js variables
let canvas;
let canvasWidth = 1200;
let canvasHeight = 600;

// Initialize parameter tracks
function initializeParameterTracks() {
    sequencer.parameterTracks = [];
    for (let i = 0; i < PARAM_NAMES.length; i++) {
        sequencer.parameterTracks.push({
            name: PARAM_NAMES[i],
            stepCount: DEFAULT_STEPS,
            currentStep: 0,
            color: PARAM_COLORS[i],
            values: new Array(MAX_STEPS).fill(0.5) // Default values
        });
    }
    
    // Initialize Gate track with binary values
    sequencer.parameterTracks[7].values = new Array(MAX_STEPS).fill(false);
    sequencer.parameterTracks[8].values = new Array(MAX_STEPS).fill(false); // Slide
    
    // Set some default gate pattern
    for (let i = 0; i < MAX_STEPS; i += 4) {
        sequencer.parameterTracks[7].values[i] = true; // Gate on every 4th step
    }
}

// Calculate least common multiple for pattern length
function gcd(a, b) {
    return b === 0 ? a : gcd(b, a % b);
}

function lcm(a, b) {
    return (a * b) / gcd(a, b);
}

function calculatePatternLength() {
    let result = sequencer.parameterTracks[0].stepCount;
    for (let i = 1; i < sequencer.parameterTracks.length; i++) {
        result = lcm(result, sequencer.parameterTracks[i].stepCount);
    }
    return Math.min(result, 256); // Cap at reasonable length
}

// Update UI elements
function updateUI() {
    document.getElementById('currentStep').textContent = sequencer.currentStep + 1;
    document.getElementById('patternLength').textContent = calculatePatternLength();
    document.getElementById('fullCycle').textContent = calculatePatternLength();
    document.getElementById('tempoValue').textContent = sequencer.tempo;
}

// Sequencer control functions
function startSequencer() {
    if (sequencer.isPlaying) return;
    
    sequencer.isPlaying = true;
    const stepDuration = (60 / sequencer.tempo / 4) * 3000; // 16th notes
    
    sequencer.stepInterval = setInterval(() => {
        advanceStep();
    }, stepDuration);
    
    document.getElementById('playBtn').textContent = 'Pause';
    document.getElementById('playBtn').classList.add('glow');
}

function stopSequencer() {
    sequencer.isPlaying = false;
    if (sequencer.stepInterval) {
        clearInterval(sequencer.stepInterval);
        sequencer.stepInterval = null;
    }
    
    document.getElementById('playBtn').textContent = 'Play';
    document.getElementById('playBtn').classList.remove('glow');
}

function resetSequencer() {
    stopSequencer();
    sequencer.currentStep = 0;
    sequencer.currentStepPerParam.fill(0);
    
    for (let track of sequencer.parameterTracks) {
        track.currentStep = 0;
    }
    
    updateUI();
}

// Core sequencer logic - matches the C++ advanceStep method
function advanceStep() {
    sequencer.currentStep = (sequencer.currentStep + 1) % calculatePatternLength();
    
    // Update each parameter track independently (polymetric)
    for (let i = 0; i < sequencer.parameterTracks.length; i++) {
        const track = sequencer.parameterTracks[i];
        sequencer.currentStepPerParam[i] = sequencer.currentStep % track.stepCount;
        track.currentStep = sequencer.currentStepPerParam[i];
    }
    
    updateUI();
}

// P5.js setup
function setup() {
    canvas = createCanvas(canvasWidth, canvasHeight);
    canvas.parent('p5-container');
    
    initializeParameterTracks();
    updateUI();
}

// P5.js draw loop
function draw() {
    background(6, 6, 6);
    
    drawParameterTracks();
    drawCurrentStepIndicators();
    drawPatternInfo();
}

// Draw parameter tracks visualization
function drawParameterTracks() {
    const trackHeight = 50;
    const trackSpacing = 60;
    const startY = 50;
    const startX = 100;
    const stepWidth = (canvasWidth - 200) / MAX_STEPS;
    
    // Draw track labels and step indicators
    for (let trackIndex = 0; trackIndex < sequencer.parameterTracks.length; trackIndex++) {
        const track = sequencer.parameterTracks[trackIndex];
        const y = startY + trackIndex * trackSpacing;
        
        // Track label
        fill(255);
        textAlign(RIGHT, CENTER);
        textSize(12);
        text(track.name, startX - 10, y + trackHeight / 2);
        
        // Track length indicator
        textAlign(LEFT, CENTER);
        textSize(10);
        fill(150);
        text(`(${track.stepCount})`, startX - 10, y + trackHeight / 2 + 15);
        
        // Draw steps
        for (let step = 0; step < MAX_STEPS; step++) {
            const x = startX + step * stepWidth;
            const isActiveStep = step < track.stepCount;
            const isCurrentStep = step === track.currentStep && isActiveStep;
            
            // Step background
            if (isActiveStep) {
                fill(isCurrentStep ? color(track.color) : color(track.color + '99'));
            } else {
                fill(20, 20, 30); // Inactive steps
            }
            
            stroke(isCurrentStep ? 255 : 100);
            strokeWeight(isCurrentStep ? 2 : 1);
            rect(x, y, stepWidth - 2, trackHeight);
            
            // Step value visualization
            if (isActiveStep) {
                const value = track.values[step];
                
                if (track.name === 'Gate' || track.name === 'Slide') {
                    // Binary parameters
                    if (value) {
                        fill(track.color);
                        ellipse(x + stepWidth / 2, y + trackHeight / 2, 20, 20);
                    }
                } else {
                    // Continuous parameters
                    const barHeight = value * (trackHeight - 10);
                    fill(track.color);
                    noStroke();
                    rect(x + 5, y + trackHeight - 5 - barHeight, stepWidth - 12, barHeight);
                }
            }
            
            // Step number
            if (isActiveStep) {
                fill(255, 150);
                textAlign(CENTER, CENTER);
                textSize(8);
                text(step + 1, x + stepWidth / 2, y - 10);
            }
        }
    }
}

// Draw current step indicators
function drawCurrentStepIndicators() {
    const startX = 100;
    const stepWidth = (canvasWidth - 200) / MAX_STEPS;
    
    // Global step indicator
    const globalX = startX + (sequencer.currentStep % MAX_STEPS) * stepWidth;
    stroke(0, 212, 255);
    strokeWeight(3);
    line(globalX, 20, globalX, height - 20);
    
    // Global step label
    fill(0, 212, 255);
    textAlign(CENTER, CENTER);
    textSize(14);
    text(`Step ${sequencer.currentStep + 1}`, globalX, 30);
}

// Draw pattern information
function drawPatternInfo() {
    const infoX = canvasWidth - 180;
    const infoY = 50;
    
    // Background
    fill(0, 0, 0, 100);
    stroke(0, 212, 255, 100);
    strokeWeight(1);
    rect(infoX, infoY, 160, 200);
    
    // Title
    fill(0, 212, 255);
    textAlign(LEFT, TOP);
    textSize(14);
    text('Pattern Analysis', infoX + 10, infoY + 10);
    
    // Pattern length
    fill(255);
    textSize(12);
    text(`Full Cycle: ${calculatePatternLength()} steps`, infoX + 10, infoY + 35);
    
    // Individual track positions
    textSize(10);
    for (let i = 0; i < Math.min(6, sequencer.parameterTracks.length); i++) {
        const track = sequencer.parameterTracks[i];
        fill(track.color);
        text(`${track.name}: ${track.currentStep + 1}/${track.stepCount}`, 
             infoX + 10, infoY + 60 + i * 15);
    }
    
    // Polyrhythmic relationships
    fill(255, 200);
    textSize(10);
    text('Polyrhythmic Ratios:', infoX + 10, infoY + 150);
    
    const baseLength = sequencer.parameterTracks[0].stepCount;
    for (let i = 1; i < Math.min(4, sequencer.parameterTracks.length); i++) {
        const track = sequencer.parameterTracks[i];
        const ratio = `${baseLength}:${track.stepCount}`;
        fill(track.color);
        text(ratio, infoX + 10, infoY + 165 + (i - 1) * 12);
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Transport controls
    document.getElementById('playBtn').addEventListener('click', function() {
        if (sequencer.isPlaying) {
            stopSequencer();
        } else {
            startSequencer();
        }
    });
    
    document.getElementById('stopBtn').addEventListener('click', stopSequencer);
    document.getElementById('resetBtn').addEventListener('click', resetSequencer);
    
    // Tempo control
    document.getElementById('tempoSlider').addEventListener('input', function(e) {
        sequencer.tempo = parseInt(e.target.value);
        updateUI();
        
        // Restart sequencer with new tempo if playing
        if (sequencer.isPlaying) {
            stopSequencer();
            startSequencer();
        }
    });
    
    // Parameter step count controls
    const paramSliders = [
        'noteSlider', 'velSlider', 'filterSlider', 'attackSlider', 'decaySlider',
        'octSlider', 'gateSizeSlider', 'gateSlider', 'slideSlider'
    ];
    
    const paramDisplays = [
        'noteSteps', 'velSteps', 'filterSteps', 'attackSteps', 'decaySteps',
        'octSteps', 'gateSizeSteps', 'gateSteps', 'slideSteps'
    ];
    
    paramSliders.forEach((sliderId, index) => {
        document.getElementById(sliderId).addEventListener('input', function(e) {
            const steps = parseInt(e.target.value);
            sequencer.parameterTracks[index].stepCount = steps;
            document.getElementById(paramDisplays[index]).textContent = steps;
            updateUI();
        });
    });
});

// Preset patterns
function loadPreset(presetName) {
    switch (presetName) {
        case 'basic':
            // All tracks same length
            sequencer.parameterTracks.forEach(track => {
                track.stepCount = 16;
            });
            break;
            
        case 'polyrhythm':
            // Classic polyrhythmic pattern
            const polyLengths = [16, 12, 8, 6, 4, 16, 3, 16, 7];
            sequencer.parameterTracks.forEach((track, i) => {
                track.stepCount = polyLengths[i];
            });
            break;
            
        case 'complex':
            // Complex polyrhythmic relationships
            const complexLengths = [2, 3, 4, 5, 6, 7, 8, 9, 10];
            sequencer.parameterTracks.forEach((track, i) => {
                track.stepCount = complexLengths[i];
            });
            break;
            
        case 'random':
            // Random step counts
            sequencer.parameterTracks.forEach(track => {
                track.stepCount = Math.floor(Math.random() * 15) + 2;
            });
            break;
    }
    
    // Update UI sliders
    const sliderIds = [
        'noteSlider', 'velSlider', 'filterSlider', 'attackSlider', 'decaySlider',
        'octSlider', 'gateSizeSlider', 'gateSlider', 'slideSlider'
    ];
    
    const displayIds = [
        'noteSteps', 'velSteps', 'filterSteps', 'attackSteps', 'decaySteps',
        'octSteps', 'gateSizeSteps', 'gateSteps', 'slideSteps'
    ];
    
    sliderIds.forEach((sliderId, index) => {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(displayIds[index]);
        slider.value = sequencer.parameterTracks[index].stepCount;
        display.textContent = sequencer.parameterTracks[index].stepCount;
    });
    
    // Reset sequencer to see changes
    resetSequencer();
    updateUI();
    
    // Add visual feedback
    document.querySelector(`[onclick="loadPreset('${presetName}')"]`).classList.add('active');
    setTimeout(() => {
        document.querySelector(`[onclick="loadPreset('${presetName}')"]`).classList.remove('active');
    }, 500);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    switch(e.code) {
        case 'Space':
            e.preventDefault();
            if (sequencer.isPlaying) {
                stopSequencer();
            } else {
                startSequencer();
            }
            break;
        case 'KeyR':
            resetSequencer();
            break;
        case 'Digit1':
            loadPreset('basic');
            break;
        case 'Digit2':
            loadPreset('polyrhythm');
            break;
        case 'Digit3':
            loadPreset('complex');
            break;
        case 'Digit4':
            loadPreset('random');
            break;
    }
});

// Window resize handler
function windowResized() {
    // Adjust canvas size if needed
    const container = document.getElementById('p5-container');
    const containerWidth = container.clientWidth - 40;
    if (containerWidth !== canvasWidth && containerWidth > 800) {
        canvasWidth = containerWidth;
        resizeCanvas(canvasWidth, canvasHeight);
    }
}