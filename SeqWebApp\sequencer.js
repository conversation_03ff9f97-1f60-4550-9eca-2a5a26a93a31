// PicoMudras Polymetric Sequencer Visualizer
// Based on the PicoMudrasSequencer project architecture

// Parameter definitions matching the C++ code
const PARAM_NAMES = [
    'Note', 'Velocity', 'Filter', 'Attack', 'Decay', 'Octave', 'Gate Length', 'Gate', 'Slide'
];

const PARAM_COLORS = [
    '#ff6b6b', // Note - Red
    '#4ecdc4', // Velocity - Teal
    '#45b7d1', // Filter - Blue
    '#96ceb4', // Attack - Green
    '#feca57', // Decay - Yellow
    '#ff9ff3', // Octave - Pink
    '#54a0ff', // Gate Length - Light Blue
    '#5f27cd', // Gate - Purple
    '#00d2d3'  // Slide - Cyan
];

const DEFAULT_STEPS = 16;
const MAX_STEPS = 16;
const MIN_STEPS = 1;

// Sequencer state
let sequencer = {
    isPlaying: false,
    currentStep: 0,
    tempo: 70,
    stepInterval: null,
    parameterTracks: [],
    currentStepPerParam: new Array(PARAM_NAMES.length).fill(0)
};

// P5.js variables
let canvas;
let canvasWidth = 1200;
let canvasHeight = 600;

// Initialize parameter tracks
function initializeParameterTracks() {
    sequencer.parameterTracks = [];
    for (let i = 0; i < PARAM_NAMES.length; i++) {
        sequencer.parameterTracks.push({
            name: PARAM_NAMES[i],
            stepCount: DEFAULT_STEPS,
            currentStep: 0,
            color: PARAM_COLORS[i],
            values: new Array(MAX_STEPS).fill(0.5) // Default values
        });
    }
    
    // Initialize Gate track with binary values
    sequencer.parameterTracks[7].values = new Array(MAX_STEPS).fill(false);
    sequencer.parameterTracks[8].values = new Array(MAX_STEPS).fill(false); // Slide
    
    // Set some default gate pattern
    for (let i = 0; i < MAX_STEPS; i += 4) {
        sequencer.parameterTracks[7].values[i] = true; // Gate on every 4th step
    }
}

// Calculate least common multiple for pattern length
function gcd(a, b) {
    return b === 0 ? a : gcd(b, a % b);
}

function lcm(a, b) {
    return (a * b) / gcd(a, b);
}

function calculatePatternLength() {
    let result = sequencer.parameterTracks[0].stepCount;
    for (let i = 1; i < sequencer.parameterTracks.length; i++) {
        result = lcm(result, sequencer.parameterTracks[i].stepCount);
    }
    return Math.min(result, 256); // Cap at reasonable length
}

// Update UI elements with enhanced visual feedback
function updateUI() {
    const currentStepElement = document.getElementById('currentStep');
    const patternLengthElement = document.getElementById('patternLength');
    const fullCycleElement = document.getElementById('fullCycle');
    const tempoValueElement = document.getElementById('tempoValue');

    // Animate step counter changes
    if (currentStepElement.textContent !== (sequencer.currentStep + 1).toString()) {
        currentStepElement.style.transform = 'scale(1.2)';
        currentStepElement.style.color = 'var(--primary-accent)';
        setTimeout(() => {
            currentStepElement.style.transform = 'scale(1)';
            currentStepElement.style.color = '';
        }, 150);
    }

    currentStepElement.textContent = sequencer.currentStep + 1;
    patternLengthElement.textContent = calculatePatternLength();
    fullCycleElement.textContent = calculatePatternLength();
    tempoValueElement.textContent = sequencer.tempo;

    // Add visual feedback for pattern changes
    const patternLength = calculatePatternLength();
    if (patternLength > 16) {
        patternLengthElement.style.color = 'var(--warning-accent)';
        fullCycleElement.style.color = 'var(--warning-accent)';
    } else {
        patternLengthElement.style.color = '';
        fullCycleElement.style.color = '';
    }
}

// Enhanced sequencer control functions with visual feedback
function startSequencer() {
    if (sequencer.isPlaying) return;

    sequencer.isPlaying = true;
    const stepDuration = (60 / sequencer.tempo / 4) * 1000; // 16th notes in milliseconds

    sequencer.stepInterval = setInterval(() => {
        advanceStep();
    }, stepDuration);

    const playBtn = document.getElementById('playBtn');
    playBtn.textContent = 'Pause';
    playBtn.classList.add('glow');

    // Add visual feedback for start
    playBtn.style.transform = 'scale(1.05)';
    setTimeout(() => {
        playBtn.style.transform = '';
    }, 200);

    // Add loading state to other controls
    document.getElementById('stopBtn').classList.remove('loading');
    document.getElementById('resetBtn').classList.remove('loading');
}

function stopSequencer() {
    sequencer.isPlaying = false;
    if (sequencer.stepInterval) {
        clearInterval(sequencer.stepInterval);
        sequencer.stepInterval = null;
    }

    const playBtn = document.getElementById('playBtn');
    playBtn.textContent = 'Play';
    playBtn.classList.remove('glow');

    // Add visual feedback for stop
    const stopBtn = document.getElementById('stopBtn');
    stopBtn.style.transform = 'scale(1.05)';
    setTimeout(() => {
        stopBtn.style.transform = '';
    }, 200);
}

function resetSequencer() {
    stopSequencer();

    sequencer.currentStep = 0;
    sequencer.currentStepPerParam.fill(0);

    for (let track of sequencer.parameterTracks) {
        track.currentStep = 0;
    }

    // Add visual feedback for reset
    const resetBtn = document.getElementById('resetBtn');
    resetBtn.classList.add('loading');
    setTimeout(() => {
        resetBtn.classList.remove('loading');
    }, 500);

    updateUI();

    // Flash effect for reset confirmation
    document.body.style.background = 'linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%)';
    setTimeout(() => {
        document.body.style.background = '';
    }, 100);
}

// Core sequencer logic - matches the C++ advanceStep method
function advanceStep() {
    sequencer.currentStep = (sequencer.currentStep + 1) % calculatePatternLength();
    
    // Update each parameter track independently (polymetric)
    for (let i = 0; i < sequencer.parameterTracks.length; i++) {
        const track = sequencer.parameterTracks[i];
        sequencer.currentStepPerParam[i] = sequencer.currentStep % track.stepCount;
        track.currentStep = sequencer.currentStepPerParam[i];
    }
    
    updateUI();
}

// P5.js setup
function setup() {
    canvas = createCanvas(canvasWidth, canvasHeight);
    canvas.parent('p5-container');
    
    initializeParameterTracks();
    updateUI();
}

// Enhanced P5.js draw loop with modern aesthetics
function draw() {
    // Modern gradient background
    for (let i = 0; i <= height; i++) {
        const inter = map(i, 0, height, 0, 1);
        const c = lerpColor(color(10, 10, 15), color(26, 26, 46), inter);
        stroke(c);
        line(0, i, width, i);
    }

    drawParameterTracks();
    drawCurrentStepIndicators();
    drawPatternInfo();
    drawPerformanceMetrics();
}

// Enhanced parameter tracks visualization with modern aesthetics
function drawParameterTracks() {
    const trackHeight = 55;
    const trackSpacing = 65;
    const startY = 60;
    const startX = 120;
    const stepWidth = (canvasWidth - 240) / MAX_STEPS;
    
    // Draw track labels and step indicators
    for (let trackIndex = 0; trackIndex < sequencer.parameterTracks.length; trackIndex++) {
        const track = sequencer.parameterTracks[trackIndex];
        const y = startY + trackIndex * trackSpacing;
        
        // Track label
        fill(255);
        textAlign(RIGHT, CENTER);
        textSize(12);
        text(track.name, startX - 10, y + trackHeight / 2);
        
        // Track length indicator
        textAlign(LEFT, CENTER);
        textSize(10);
        fill(150);
        text(`(${track.stepCount})`, startX - 10, y + trackHeight / 2 + 15);
        
        // Draw steps with enhanced visual effects
        for (let step = 0; step < MAX_STEPS; step++) {
            const x = startX + step * stepWidth;
            const isActiveStep = step < track.stepCount;
            const isCurrentStep = step === track.currentStep && isActiveStep;

            // Step background with gradient and glow effects
            if (isActiveStep) {
                if (isCurrentStep) {
                    // Current step with glow effect
                    drawingContext.shadowColor = track.color;
                    drawingContext.shadowBlur = 15;
                    fill(color(track.color));
                } else {
                    drawingContext.shadowBlur = 0;
                    fill(color(track.color + '66'));
                }
            } else {
                drawingContext.shadowBlur = 0;
                fill(15, 15, 25); // Inactive steps
            }

            stroke(isCurrentStep ? color(track.color) : color(100, 100, 120));
            strokeWeight(isCurrentStep ? 3 : 1);

            // Rounded rectangles for modern look
            const cornerRadius = 6;
            rect(x + 1, y + 1, stepWidth - 4, trackHeight - 2, cornerRadius);

            // Reset shadow
            drawingContext.shadowBlur = 0;
            
            // Enhanced step value visualization
            if (isActiveStep) {
                const value = track.values[step];

                if (track.name === 'Gate' || track.name === 'Slide') {
                    // Binary parameters with enhanced visual feedback
                    if (value) {
                        // Glow effect for active binary parameters
                        drawingContext.shadowColor = track.color;
                        drawingContext.shadowBlur = 10;
                        fill(color(track.color));
                        noStroke();
                        ellipse(x + stepWidth / 2, y + trackHeight / 2, 24, 24);

                        // Inner highlight
                        fill(255, 255, 255, 100);
                        ellipse(x + stepWidth / 2, y + trackHeight / 2, 12, 12);
                        drawingContext.shadowBlur = 0;
                    }
                } else {
                    // Continuous parameters with gradient bars
                    const barHeight = value * (trackHeight - 12);
                    const barWidth = stepWidth - 16;

                    // Gradient fill for parameter bars
                    noStroke();
                    for (let i = 0; i < barHeight; i++) {
                        const alpha = map(i, 0, barHeight, 0.3, 1.0);
                        fill(red(color(track.color)), green(color(track.color)), blue(color(track.color)), alpha * 255);
                        rect(x + 8, y + trackHeight - 6 - i, barWidth, 1);
                    }

                    // Top highlight
                    if (barHeight > 2) {
                        fill(255, 255, 255, 80);
                        rect(x + 8, y + trackHeight - 6 - barHeight, barWidth, 2);
                    }
                }
            }
            
            // Step number
            if (isActiveStep) {
                fill(255, 150);
                textAlign(CENTER, CENTER);
                textSize(8);
                text(step + 1, x + stepWidth / 2, y - 10);
            }
        }
    }
}

// Enhanced current step indicators with modern effects
function drawCurrentStepIndicators() {
    const startX = 120;
    const stepWidth = (canvasWidth - 240) / MAX_STEPS;

    // Global step indicator with glow effect
    const globalX = startX + (sequencer.currentStep % MAX_STEPS) * stepWidth + stepWidth / 2;

    // Animated glow line
    drawingContext.shadowColor = '#00d4ff';
    drawingContext.shadowBlur = 20;
    stroke(0, 212, 255);
    strokeWeight(4);
    line(globalX, 30, globalX, height - 30);

    // Pulsing effect for current step
    const pulseSize = 5 + sin(millis() * 0.01) * 3;
    drawingContext.shadowBlur = 15;
    strokeWeight(pulseSize);
    line(globalX, 30, globalX, height - 30);

    // Reset shadow
    drawingContext.shadowBlur = 0;

    // Enhanced step label with background
    fill(0, 0, 0, 150);
    noStroke();
    rect(globalX - 35, 15, 70, 25, 12);

    fill(0, 212, 255);
    textAlign(CENTER, CENTER);
    textSize(12);
    textStyle(BOLD);
    text(`Step ${sequencer.currentStep + 1}`, globalX, 27);
    textStyle(NORMAL);
}

// Enhanced pattern information display
function drawPatternInfo() {
    const infoX = canvasWidth - 200;
    const infoY = 60;
    const panelWidth = 180;
    const panelHeight = 220;

    // Modern glass-morphism background
    fill(0, 0, 0, 120);
    stroke(0, 212, 255, 80);
    strokeWeight(1);
    rect(infoX, infoY, panelWidth, panelHeight, 12);

    // Gradient overlay
    for (let i = 0; i < panelHeight; i++) {
        const alpha = map(i, 0, panelHeight, 30, 5);
        fill(0, 212, 255, alpha);
        noStroke();
        rect(infoX, infoY + i, panelWidth, 1);
    }

    // Title with glow effect
    drawingContext.shadowColor = '#00d4ff';
    drawingContext.shadowBlur = 10;
    fill(0, 212, 255);
    textAlign(LEFT, TOP);
    textSize(16);
    textStyle(BOLD);
    text('Pattern Analysis', infoX + 15, infoY + 15);
    drawingContext.shadowBlur = 0;
    textStyle(NORMAL);

    // Pattern length with visual indicator
    fill(255, 255, 255);
    textSize(13);
    const patternLength = calculatePatternLength();
    text(`Full Cycle: ${patternLength} steps`, infoX + 15, infoY + 45);

    // Progress bar for pattern completion
    const progress = (sequencer.currentStep + 1) / patternLength;
    fill(0, 212, 255, 100);
    rect(infoX + 15, infoY + 65, panelWidth - 30, 4, 2);
    fill(0, 212, 255);
    rect(infoX + 15, infoY + 65, (panelWidth - 30) * progress, 4, 2);

    // Individual track positions with color coding
    textSize(11);
    for (let i = 0; i < Math.min(6, sequencer.parameterTracks.length); i++) {
        const track = sequencer.parameterTracks[i];
        const y = infoY + 85 + i * 18;

        // Track color indicator
        fill(track.color);
        noStroke();
        ellipse(infoX + 20, y + 5, 8, 8);

        // Track info
        fill(255, 255, 255);
        text(`${track.name}: ${track.currentStep + 1}/${track.stepCount}`,
             infoX + 30, y);
    }

    // Polyrhythmic relationships
    fill(255, 255, 255, 180);
    textSize(11);
    textStyle(BOLD);
    text('Polyrhythmic Ratios:', infoX + 15, infoY + 200);
    textStyle(NORMAL);

    const baseLength = sequencer.parameterTracks[0].stepCount;
    textSize(10);
    for (let i = 1; i < Math.min(3, sequencer.parameterTracks.length); i++) {
        const track = sequencer.parameterTracks[i];
        const ratio = `${baseLength}:${track.stepCount}`;
        fill(track.color);
        text(ratio, infoX + 15, infoY + 215 + (i - 1) * 12);
    }
}

// New performance metrics display
function drawPerformanceMetrics() {
    const metricsX = 20;
    const metricsY = height - 80;

    // Background panel
    fill(0, 0, 0, 100);
    stroke(100, 100, 100, 100);
    strokeWeight(1);
    rect(metricsX, metricsY, 160, 60, 8);

    // FPS counter
    fill(0, 212, 255);
    textAlign(LEFT, TOP);
    textSize(10);
    text(`FPS: ${Math.round(frameRate())}`, metricsX + 10, metricsY + 10);

    // Tempo display
    fill(255, 255, 255);
    text(`Tempo: ${sequencer.tempo} BPM`, metricsX + 10, metricsY + 25);

    // Playing status
    fill(sequencer.isPlaying ? color(78, 205, 196) : color(255, 107, 107));
    text(`Status: ${sequencer.isPlaying ? 'Playing' : 'Stopped'}`, metricsX + 10, metricsY + 40);
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Transport controls
    document.getElementById('playBtn').addEventListener('click', function() {
        if (sequencer.isPlaying) {
            stopSequencer();
        } else {
            startSequencer();
        }
    });
    
    document.getElementById('stopBtn').addEventListener('click', stopSequencer);
    document.getElementById('resetBtn').addEventListener('click', resetSequencer);
    
    // Enhanced tempo control with visual feedback
    document.getElementById('tempoSlider').addEventListener('input', function(e) {
        sequencer.tempo = parseInt(e.target.value);
        updateUI();

        // Visual feedback for tempo change
        const tempoValue = document.getElementById('tempoValue');
        tempoValue.style.transform = 'scale(1.2)';
        tempoValue.style.color = 'var(--warning-accent)';
        setTimeout(() => {
            tempoValue.style.transform = 'scale(1)';
            tempoValue.style.color = '';
        }, 200);

        // Restart sequencer with new tempo if playing
        if (sequencer.isPlaying) {
            stopSequencer();
            startSequencer();
        }
    });
    
    // Parameter step count controls
    const paramSliders = [
        'noteSlider', 'velSlider', 'filterSlider', 'attackSlider', 'decaySlider',
        'octSlider', 'gateSizeSlider', 'gateSlider', 'slideSlider'
    ];
    
    const paramDisplays = [
        'noteSteps', 'velSteps', 'filterSteps', 'attackSteps', 'decaySteps',
        'octSteps', 'gateSizeSteps', 'gateSteps', 'slideSteps'
    ];
    
    paramSliders.forEach((sliderId, index) => {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(paramDisplays[index]);

        slider.addEventListener('input', function(e) {
            const steps = parseInt(e.target.value);
            sequencer.parameterTracks[index].stepCount = steps;
            display.textContent = steps;

            // Visual feedback for parameter change
            display.style.transform = 'scale(1.15)';
            display.style.color = sequencer.parameterTracks[index].color;
            setTimeout(() => {
                display.style.transform = 'scale(1)';
                display.style.color = '';
            }, 300);

            // Add glow effect to the parameter control
            const paramControl = slider.closest('.param-control');
            paramControl.style.boxShadow = `0 0 20px ${sequencer.parameterTracks[index].color}40`;
            setTimeout(() => {
                paramControl.style.boxShadow = '';
            }, 500);

            updateUI();
        });

        // Add hover effects
        slider.addEventListener('mouseenter', function() {
            const paramControl = slider.closest('.param-control');
            paramControl.style.transform = 'translateY(-2px)';
        });

        slider.addEventListener('mouseleave', function() {
            const paramControl = slider.closest('.param-control');
            paramControl.style.transform = '';
        });
    });
});

// Preset patterns
function loadPreset(presetName) {
    switch (presetName) {
        case 'basic':
            // All tracks same length
            sequencer.parameterTracks.forEach(track => {
                track.stepCount = 16;
            });
            break;
            
        case 'polyrhythm':
            // Classic polyrhythmic pattern
            const polyLengths = [16, 12, 8, 6, 4, 16, 3, 16, 7];
            sequencer.parameterTracks.forEach((track, i) => {
                track.stepCount = polyLengths[i];
            });
            break;
            
        case 'complex':
            // Complex polyrhythmic relationships
            const complexLengths = [2, 3, 4, 5, 6, 7, 8, 9, 10];
            sequencer.parameterTracks.forEach((track, i) => {
                track.stepCount = complexLengths[i];
            });
            break;
            
        case 'random':
            // Random step counts
            sequencer.parameterTracks.forEach(track => {
                track.stepCount = Math.floor(Math.random() * 15) + 2;
            });
            break;
    }
    
    // Update UI sliders
    const sliderIds = [
        'noteSlider', 'velSlider', 'filterSlider', 'attackSlider', 'decaySlider',
        'octSlider', 'gateSizeSlider', 'gateSlider', 'slideSlider'
    ];
    
    const displayIds = [
        'noteSteps', 'velSteps', 'filterSteps', 'attackSteps', 'decaySteps',
        'octSteps', 'gateSizeSteps', 'gateSteps', 'slideSteps'
    ];
    
    sliderIds.forEach((sliderId, index) => {
        const slider = document.getElementById(sliderId);
        const display = document.getElementById(displayIds[index]);
        slider.value = sequencer.parameterTracks[index].stepCount;
        display.textContent = sequencer.parameterTracks[index].stepCount;
    });
    
    // Reset sequencer to see changes
    resetSequencer();
    updateUI();
    
    // Enhanced visual feedback for preset loading
    const presetButton = document.querySelector(`[onclick="loadPreset('${presetName}')"]`);
    presetButton.classList.add('active');
    presetButton.style.transform = 'scale(1.1)';

    // Show loading state
    const originalText = presetButton.textContent;
    presetButton.textContent = 'Loading...';
    presetButton.classList.add('loading');

    setTimeout(() => {
        presetButton.classList.remove('active', 'loading');
        presetButton.style.transform = '';
        presetButton.textContent = originalText;

        // Success feedback
        presetButton.classList.add('success');
        setTimeout(() => {
            presetButton.classList.remove('success');
        }, 1000);
    }, 800);
}

// Enhanced keyboard shortcuts with accessibility
document.addEventListener('keydown', function(e) {
    // Don't interfere with form inputs
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
    }

    switch(e.code) {
        case 'Space':
            e.preventDefault();
            const playBtn = document.getElementById('playBtn');
            if (sequencer.isPlaying) {
                stopSequencer();
                playBtn.setAttribute('aria-pressed', 'false');
            } else {
                startSequencer();
                playBtn.setAttribute('aria-pressed', 'true');
            }
            // Visual feedback for keyboard activation
            playBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                playBtn.style.transform = '';
            }, 100);
            break;

        case 'KeyR':
            e.preventDefault();
            resetSequencer();
            // Visual feedback
            const resetBtn = document.getElementById('resetBtn');
            resetBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                resetBtn.style.transform = '';
            }, 100);
            break;

        case 'Digit1':
            e.preventDefault();
            loadPreset('basic');
            break;

        case 'Digit2':
            e.preventDefault();
            loadPreset('polyrhythm');
            break;

        case 'Digit3':
            e.preventDefault();
            loadPreset('complex');
            break;

        case 'Digit4':
            e.preventDefault();
            loadPreset('random');
            break;

        case 'ArrowUp':
            e.preventDefault();
            // Increase tempo
            const tempoSlider = document.getElementById('tempoSlider');
            const newTempo = Math.min(200, sequencer.tempo + 5);
            tempoSlider.value = newTempo;
            tempoSlider.dispatchEvent(new Event('input'));
            break;

        case 'ArrowDown':
            e.preventDefault();
            // Decrease tempo
            const tempoSliderDown = document.getElementById('tempoSlider');
            const newTempoDown = Math.max(60, sequencer.tempo - 5);
            tempoSliderDown.value = newTempoDown;
            tempoSliderDown.dispatchEvent(new Event('input'));
            break;

        case 'Escape':
            e.preventDefault();
            // Stop sequencer and reset focus
            if (sequencer.isPlaying) {
                stopSequencer();
                document.getElementById('playBtn').setAttribute('aria-pressed', 'false');
            }
            document.activeElement.blur();
            break;
    }
});

// Enhanced window resize handler with accessibility considerations
function windowResized() {
    // Adjust canvas size if needed
    const container = document.getElementById('p5-container');
    const containerWidth = container.clientWidth - 40;
    if (containerWidth !== canvasWidth && containerWidth > 800) {
        canvasWidth = containerWidth;
        resizeCanvas(canvasWidth, canvasHeight);

        // Update aria-label with new dimensions for screen readers
        const canvas = document.querySelector('#p5-container canvas');
        if (canvas) {
            canvas.setAttribute('aria-label',
                `Interactive sequencer visualization, ${canvasWidth} by ${canvasHeight} pixels, showing parameter tracks and current playback position`);
        }
    }
}

// Focus management for accessibility
function initializeFocusManagement() {
    // Add focus indicators to all interactive elements
    const interactiveElements = document.querySelectorAll('button, input[type="range"]');

    interactiveElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid var(--primary-accent)';
            this.style.outlineOffset = '2px';
        });

        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });

    // Skip link for keyboard navigation
    const skipLink = document.createElement('a');
    skipLink.href = '#p5-container';
    skipLink.textContent = 'Skip to visualization';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: var(--primary-accent);
        color: var(--primary-bg);
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        transition: top 0.3s;
    `;

    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });

    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
}

// Initialize accessibility features when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeFocusManagement();

    // Set initial ARIA attributes
    document.getElementById('playBtn').setAttribute('aria-pressed', 'false');

    // Update slider aria-valuenow attributes
    const sliders = document.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
        slider.addEventListener('input', function() {
            this.setAttribute('aria-valuenow', this.value);
        });
    });
});