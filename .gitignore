# Build directory
build/
build/*
PROGRAMMERS_MANUAL.md
.vscode/
*.swp
*~
.roo/
DUAL_MPR121_IMPLEMENTATION_PLAN.md

CMakeFiles/
CMakeCache.txt
cmake_install.cmake
src/audio/GEMINI.md
src/LEDMatrix/LEDController_README.md
src/sensors/GEMINI.md
src/midi/GEMINI.md
src/sequencer/GEMINI.md
src/ui/GEMINI.md
src/LEDMatrix/LEDController_README.md
PROGRAMMERS_MANUAL.md
GEMINI.md
.roo/rules/
test_midi_implementation.cpp