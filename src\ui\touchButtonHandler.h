#ifndef TOUCHBUTTONHANDLER_H
#define TOUCHBUTTONHANDLER_H

#include <cstdint>
#include "UIState.h"

// Forward declarations
class Sequencer;

// Function to be called in the main loop to handle touch button events.
void handleTouchButtons(UIState& uiState, Sequencer& seq1, Sequencer& seq2);

// These external variables should be defined in the main .ino file or another central location.
// They indicate whether the touch sensor hardware is present and initialized.
extern bool touchButtonsPresent;
extern bool touchMatrixPresent;

// Constants defining the layout of the touch buttons.
constexpr uint8_t kNumButtons = 12; // 12 touch buttons on MPR121 @ 0x5D

// Touch Buttons (MPR121 @ 0x5D) - Control Mode Constants
constexpr uint8_t BUTTON_PLAY_STOP = 0;
constexpr uint8_t BUTTON_CHANGE_SCALE = 1;
constexpr uint8_t BUTTON_CHANGE_THEME = 2;
constexpr uint8_t BUTTON_SELECT_TEMPO = 3;
constexpr uint8_t BUTTON_SELECT_SHUFFLE = 4;
constexpr uint8_t BUTTON_SELECT_DELAY_TIME = 5;
constexpr uint8_t BUTTON_SELECT_DELAY_FEEDBACK = 6;
constexpr uint8_t BUTTON_TOGGLE_DELAY = 7;
constexpr uint8_t BUTTON_LFO1_ASSIGN = 8;
constexpr uint8_t BUTTON_LFO2_ASSIGN = 9;
constexpr uint8_t BUTTON_RANDOMIZE_SEQ1 = 10; // short press randomize, long press reset
constexpr uint8_t BUTTON_RANDOMIZE_SEQ2 = 11; // short press randomize, long press reset



// Helper functions for touch button handling
void handleParameterMode(uint8_t buttonIndex, bool pressed, UIState &uiState);
void handleControlMode(uint8_t buttonIndex, bool pressed, UIState &uiState, Sequencer &seq1, Sequencer &seq2);
bool isLongPress(unsigned long pressDuration);

#endif // TOUCHBUTTONHANDLER_H
