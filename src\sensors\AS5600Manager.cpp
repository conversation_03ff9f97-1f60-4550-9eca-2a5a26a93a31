#include "AS5600Manager.h"
#include <Arduino.h>
#include "../sequencer/SequencerDefs.h"
#include "../ui/UIState.h"
#include "../GlobalVariables.h"
#include "as5600.h"
#include <algorithm>
#include "../dsp/oscillator.h"
#include "../sequencer/ParameterManager.h"

// =======================
//   EXTERNAL REFERENCES
// =======================


extern ParameterManager parameterManager;

// =======================
//   AS5600 GLOBAL VARIABLES
// =======================

// AS5600 global variables moved from main file
// Note: currentAS5600Parameter is now accessed via UIState.currentAS5600Parameter
AS5600BaseValues as5600BaseValuesVoice1;
AS5600BaseValues as5600BaseValuesVoice2;
GlobalLFOParams globalLFOs; // Definition of the global LFO parameters
unsigned long lastAS5600ButtonPress = 0;

// Flash speed zones for dynamic boundary proximity feedback
const FlashSpeedConfig FLASH_SPEED_ZONES[] = {
    {1.0f, 0.0f, 0.65f},    // Normal: 1x speed,
    {2.0f, 0.65f, 0.8375f}, // Warning: 2x speed,
    {3.0f, 0.8375f, 1.0f}   // Critical: 3x speed,
};

// =======================
//   AS5600 PARAMETER BOUNDS MANAGEMENT
// =======================

float getParameterMinValue(AS5600ParameterMode param)
{
    // Return the minimum valid value for each parameter type
    switch (param)
    {
    case AS5600ParameterMode::Vel:
    case AS5600ParameterMode::Filter:
    case AS5600ParameterMode::Attack:
    case AS5600ParameterMode::Decay:
        return 0.0f;
    case AS5600ParameterMode::DelayTime:
        return 480.0f; // 10ms minimum delay (480 samples at 48kHz)
    case AS5600ParameterMode::DelayFeedback:
        return 0.0f;
    case AS5600ParameterMode::LFO1freq:
        return 0.0001f;
    case AS5600ParameterMode::LFO2freq:
        return 0.0001f; // 0.1Hz minimum LFO frequency
    case AS5600ParameterMode::LFO1amp:
    case AS5600ParameterMode::LFO2amp:
        return 0.0f; // 0% minimum amplitude
        case AS5600ParameterMode::Tempo:
        return 30.0f; // 30BPM minimum tempo
        case AS5600ParameterMode::Shuffle:
        return 0.0f; // No minimum shuffle
    default:
        return 0.0f;
    }
}

float getParameterMaxValue(AS5600ParameterMode param)
{
    // Return the maximum valid value for each parameter type
    switch (param)
    {
    case AS5600ParameterMode::Vel:
    case AS5600ParameterMode::Filter:
    case AS5600ParameterMode::Attack:
    case AS5600ParameterMode::Decay:
        return 1.0f;
    case AS5600ParameterMode::DelayTime:
        return MAX_DELAY_SAMPLES * .75f; // Maximum delay in samples (1.8 seconds)
    case AS5600ParameterMode::DelayFeedback:
        return 0.91f; // Maximum 95% feedback to prevent excessive feedback
    case AS5600ParameterMode::LFO1freq:
    case AS5600ParameterMode::LFO2freq:
        return 8.0f; // 20Hz maximum LFO frequency
    case AS5600ParameterMode::LFO1amp:
    case AS5600ParameterMode::LFO2amp:
        return 1.0f; // 100% maximum amplitude
    case AS5600ParameterMode::Tempo:
        return 200.0f; // 120BPM maximum tempo
    case AS5600ParameterMode::Shuffle:
        return 15.0f; // 100% maximum amplitude
    default:
        return 1.0f;
    }
}

float getAS5600BaseValueRange(AS5600ParameterMode param)
{
    // Return the allowed range for AS5600 base values
    float fullRange = getParameterMaxValue(param) - getParameterMinValue(param);

    // Delay and LFO parameters use full range without restrictions
    if (param == AS5600ParameterMode::DelayTime || param == AS5600ParameterMode::DelayFeedback ||
        param == AS5600ParameterMode::LFO1freq || param == AS5600ParameterMode::LFO1amp ||
        param == AS5600ParameterMode::LFO2freq || param == AS5600ParameterMode::LFO2amp || param == AS5600ParameterMode::Tempo || param == AS5600ParameterMode::Shuffle)
    {
        return fullRange; // Full range for delay and LFO parameters
    }

    // Other parameters use reduced range to leave room for sequencer values
    return fullRange * 0.75f; // 60% of full range for sequencer parameters
}

float clampAS5600BaseValue(AS5600ParameterMode param, float value)
{
    // Clamp AS5600 base values to their allowed bidirectional range
    float maxRange = getAS5600BaseValueRange(param);
    return std::max(-maxRange, std::min(value, maxRange));
}
// --- Update AS5600 Base Values (Bidirectional Vel-Sensitive Control) ---
void updateAS5600BaseValues(UIState& UIState_param)
{
    if (!as5600Sensor.isConnected())
    {
        return;
    }

    // Get current AS5600 base values for the active voice
    AS5600BaseValues *activeBaseValues = UIState_param.isVoice2Mode ? &as5600BaseValuesVoice2 : &as5600BaseValuesVoice1;

    // Get bidirectional Vel-sensitive parameter increment
    float minVal = getParameterMinValue(UIState_param.currentAS5600Parameter);
    float maxVal = getParameterMaxValue(UIState_param.currentAS5600Parameter);
    float increment = as5600Sensor.getParameterIncrement(minVal - maxVal, maxVal - minVal, 3);

    // Ignore tiny increments to prevent noise from affecting parameters
    const float MINIMUM_INCREMENT_THRESHOLD = 0.0005f;
    if (abs(increment) < MINIMUM_INCREMENT_THRESHOLD)
    {
        return;
    }

    // Apply increment to the appropriate parameter with boundary checking
    applyIncrementToParameter(activeBaseValues, UIState_param.currentAS5600Parameter, increment);
}

void setControlTarget(UIState& uiState, AS5600ParameterMode target)
{
    uiState.currentAS5600Parameter = target;
}

// Helper function to apply increment with boundary checking
void applyIncrementToParameter(AS5600BaseValues *baseValues, AS5600ParameterMode param, float increment)
{
    float *targetValue = nullptr;

    // Select the appropriate parameter to modify
    switch (param)
    {
    case AS5600ParameterMode::Vel:
        targetValue = &baseValues->Vel;
        break;
    case AS5600ParameterMode::Filter:
        targetValue = &baseValues->filter;
        break;
    case AS5600ParameterMode::Attack:
        targetValue = &baseValues->attack;
        break;
    case AS5600ParameterMode::Decay:
        targetValue = &baseValues->decay;
        break;
    case AS5600ParameterMode::DelayTime:
        targetValue = &globalParams.delayTime;
        break;
    case AS5600ParameterMode::DelayFeedback:
        targetValue = &globalParams.delayFeedback;
        break;
    case AS5600ParameterMode::LFO1freq:
        targetValue = &globalLFOs.lfo1freq;
        break;
    case AS5600ParameterMode::LFO1amp:
        targetValue = &globalLFOs.lfo1amp;
        break;
    case AS5600ParameterMode::LFO2freq:
        targetValue = &globalLFOs.lfo2freq;
        break;
    case AS5600ParameterMode::LFO2amp:
        targetValue = &globalLFOs.lfo2amp;
        break;
    default:
        return; // Invalid parameter
    }

    if (!targetValue)
    {
        return; // Safety check
    }

    // Apply increment with boundary checking
    float newValue = *targetValue + increment;
    float oldValue = *targetValue;

    // For bidirectional parameters (voice parameters)
    if (param <= AS5600ParameterMode::Decay)
    {
        float maxRange = getAS5600BaseValueRange(param);
        *targetValue = std::max(-maxRange, std::min(newValue, maxRange));
    }
    // For unidirectional parameters (delay and LFO)
    else
    {
        float minVal = getParameterMinValue(param);
        float maxVal = getParameterMaxValue(param);
        *targetValue = std::max(minVal, std::min(newValue, maxVal));
    }

    // Debug output for delay parameter changes (uncomment for debugging)
    /*
    if (param == AS5600ParameterMode::DelayTime || param == AS5600ParameterMode::DelayFeedback)
    {
        Serial.print("AS5600 ");
        Serial.print(param == AS5600ParameterMode::DelayTime ? "DelayTime" : "DelayFeedback");
        Serial.print(" changed from ");
        Serial.print(oldValue, 3);
        Serial.print(" to ");
        Serial.print(*targetValue, 3);
        Serial.print(" (increment: ");
        Serial.print(increment, 3);
        Serial.println(")");
    }
    */
}

// Helper function for the "Shift and Scale" mapping.
// This function takes a sequencer value (0.0-1.0) and an AS5600 offset
// (a bipolar value, e.g., -0.6 to 0.6) and combines them intelligently.
float shiftAndScale(float seqValue, float as5600Offset)
{
    float finalValue;
    if (as5600Offset >= 0.0f)
    {
        // When the AS5600 offset is positive, it sets the minimum value,
        // and the sequencer value is scaled to fit the remaining range up to 1.0.
        finalValue = as5600Offset + (seqValue * (1.0f - as5600Offset));
    }
    else
    {
        // When the AS5600 offset is negative, it reduces the maximum value,
        // and the sequencer value is scaled to fit the range from 0.0 up to that new maximum.
        finalValue = seqValue * (1.0f + as5600Offset);
    }
    // Clamp the result to ensure it remains within the valid [0.0, 1.0] range.
    return std::max(0.0f, std::min(finalValue, 1.0f));
}

/**
 * Apply AS5600 magnetic encoder base values to voice parameters.
 * Implements a "Shift and Scale" mapping to combine encoder and sequencer values.
 * This avoids "dead zones" by scaling the sequencer's output within the range
 * defined by the encoder's offset.
 * */
void applyAS5600BaseValues(VoiceState *voiceState, const UIState& UIState_param)
{
    if (!as5600Sensor.isConnected() || !voiceState)
    {
        return;
    }

    const AS5600BaseValues *baseValues = UIState_param.isVoice2Mode ? &as5600BaseValuesVoice2 : &as5600BaseValuesVoice1;

    // Apply "Shift and Scale" for each parameter.
    // This maps the sequencer value into the dynamic range set by the AS5600 offset.
    voiceState->Vel = shiftAndScale(voiceState->Vel, baseValues->Vel);
    voiceState->filter = shiftAndScale(voiceState->filter, baseValues->filter);
    voiceState->attack = shiftAndScale(voiceState->attack, baseValues->attack);
    voiceState->decay = shiftAndScale(voiceState->decay, baseValues->decay);
}

/**
 * Apply AS5600 magnetic encoder values to global delay effect parameters.
 * Direct parameter control: delay parameters use full range without restrictions.
 * Thread-safe communication for Core0 audio processing.
 */
void applyAS5600DelayValues()
{
    if (!as5600Sensor.isConnected())
    {
        return;
    }

    // Directly update global delay parameters
    // These are read by the audio core, so access should be thread-safe if needed,
    // but for now we assume direct write is safe enough for this architecture.
    // Delay parameters are global and managed through globalParams
    // globalParams.delayTime and globalParams.delayFeedback are updated elsewhere
}

/**
 * Apply AS5600 magnetic encoder values to global LFO parameters.
 * Direct parameter control: LFO parameters use full range without restrictions.
 */
void applyAS5600LFOValues()
{
    if (!as5600Sensor.isConnected())
    {
        return;
    }

    // Directly update global LFO parameters
    globalParams.lfo1freq = globalLFOs.lfo1freq;
    globalParams.lfo1amp = globalLFOs.lfo1amp;
    globalParams.lfo2freq = globalLFOs.lfo2freq;
    globalParams.lfo2amp = globalLFOs.lfo2amp;
}

// =======================
//   AS5600 HELPER FUNCTIONS (moved from main file)
// =======================

/**
 * Gets the current value of the active AS5600 parameter, normalized to a 0.0-1.0 range.
 * This is used for visual feedback, such as controlling the brightness or color of an LED.
 */
float getAS5600ParameterValue(const UIState& UIState)
{
    if (!as5600Sensor.isConnected())
    {
        return 0.0f;
    }

    const AS5600BaseValues *activeBaseValues = UIState.isVoice2Mode ? &as5600BaseValuesVoice2 : &as5600BaseValuesVoice1;
    float value = 0.0f;

    // Retrieve the raw value for the current parameter
    switch (UIState.currentAS5600Parameter)
    {
    case AS5600ParameterMode::Vel:
        value = activeBaseValues->Vel;
        break;
    case AS5600ParameterMode::Filter:
        value = activeBaseValues->filter;
        break;
    case AS5600ParameterMode::Attack:
        value = activeBaseValues->attack;
        break;
    case AS5600ParameterMode::Decay:
        value = activeBaseValues->decay;
        break;
    case AS5600ParameterMode::DelayTime:
        value = globalParams.delayTime;
        break;
    case AS5600ParameterMode::DelayFeedback:
        value = globalParams.delayFeedback;
        break;
    case AS5600ParameterMode::LFO1freq:
        value = globalLFOs.lfo1freq;
        break;
    case AS5600ParameterMode::LFO1amp:
        value = globalLFOs.lfo1amp;
        break;
    case AS5600ParameterMode::LFO2freq:
        value = globalLFOs.lfo2freq;
        break;
    case AS5600ParameterMode::LFO2amp:
        value = globalLFOs.lfo2amp;
        break;
    }

    // Normalize the value to a 0.0-1.0 range for LED feedback
    float minVal = getParameterMinValue(UIState.currentAS5600Parameter);
    float maxVal = getParameterMaxValue(UIState.currentAS5600Parameter);
    float normalizedValue = (value - minVal) / (maxVal - minVal);

    // For bipolar parameters (like Vel, filter, etc.), we need to handle the normalization differently.
    // Since they range from -maxRange to +maxRange, we can map this to 0.0-1.0.
    if (UIState.currentAS5600Parameter <= AS5600ParameterMode::Decay) // Assuming these are the bipolar params
    {
        float maxRange = getAS5600BaseValueRange(UIState.currentAS5600Parameter);
        normalizedValue = (value + maxRange) / (2 * maxRange);
    }

    return std::max(0.0f, std::min(normalizedValue, 1.0f)); // Clamp to ensure valid range
}

/**
 * Initialize AS5600 base values with proper defaults
 */
void initAS5600BaseValues()
{
    // Initialize AS5600 base values with proper defaults
    // Voice parameters start at neutral (0.0f)
    as5600BaseValuesVoice1.Vel = 0.0f;
    as5600BaseValuesVoice1.filter = 0.0f;
    as5600BaseValuesVoice1.attack = 0.0f;
    as5600BaseValuesVoice1.decay = 0.0f;

    as5600BaseValuesVoice2.Vel = 0.0f;
    as5600BaseValuesVoice2.filter = 0.0f;
    as5600BaseValuesVoice2.attack = 0.0f;
    as5600BaseValuesVoice2.decay = 0.0f;

    // Delay parameters start with reasonable defaults (full range values)
    // Reset delay parameters to defaults (shared between voices)
    // Note: delayTime and delayFeedback are global parameters, not per-voice
    delayTarget = 48000.0f * 0.2f; // 200ms default delay
    feedbackAmmount = 0.55f;       // 55% default feedback

    // LFO parameters start with reasonable defaults
    globalLFOs.lfo1freq = .01f;   // 1Hz default
    globalLFOs.lfo1amp = 0.0f;  // No modulation initially
    globalLFOs.lfo2freq = .01f; // 2Hz default
    globalLFOs.lfo2amp = 0.0f;  // No modulation initially
}

/**
 * Reset AS5600 base values to defaults
 * @param UIState Reference to the UI state for voice mode information
 * @param currentVoiceOnly If true, only reset the currently active voice
 */
void resetAS5600BaseValues(UIState& UIState_param, bool currentVoiceOnly)
{
    if (currentVoiceOnly)
    {
        // Reset only the currently active voice
        AS5600BaseValues *activeBaseValues = UIState_param.isVoice2Mode ? &as5600BaseValuesVoice2 : &as5600BaseValuesVoice1;

        // Reset voice parameters to neutral
        activeBaseValues->Vel = 0.0f;
        activeBaseValues->filter = 0.0f;
        activeBaseValues->attack = 0.0f;
        activeBaseValues->decay = 0.0f;

        // LFO parameters are global and not reset with this function
    }
    else
    {
        // Reset all voices - call the full initialization
        initAS5600BaseValues();
    }
}