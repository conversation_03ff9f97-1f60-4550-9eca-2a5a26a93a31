#include "ParameterManager.h"
#include <algorithm>                  // For std::max, std::min
#include <cmath>                      // For roundf
#include <random>                     // For std::default_random_engine, std::uniform_real_distribution
#include <chrono>                     // For std::chrono::system_clock (for seeding)
#include <variant>                    // For std::visit
#include "../sensors/as5600.h"        // For AS5600ParameterMode
#include "../sensors/AS5600Manager.h" // For MAX_DELAY_SAMPLES extern declaration
#include <uClock.h>                   // For uClock global object
#include "../HardwareConfig.h"        // For centralized configuration constants

// AS5600 parameter bounds management functions moved to src/sensors/AS5600Manager.cpp

ParameterManager::ParameterManager()
{
    // Initialize the spin lock for operations that still need synchronization
    _lock = spin_lock_init(spin_lock_claim_unused(true)); // Claim a unique lock number

    // Initialize atomic pointers for double buffering
    // Core0 reads from bufferA, Core1 writes to bufferB initially
    readBuffer_.store(&bufferA_);
    writeBuffer_.store(&bufferB_);
}

void ParameterManager::init()
{
    // Initialize both buffer sets with default values
    bufferA_.init();
    bufferB_.init();
}

void ParameterManager::setStepCount(ParamId id, uint8_t steps)
{
    // Core1 (UI/Sequencer) writes to write buffer - no lock needed
    ParameterBufferSet* writeBuffer = writeBuffer_.load();
    writeBuffer->tracks[static_cast<size_t>(id)].resize(steps);
}

uint8_t ParameterManager::getStepCount(ParamId id) const
{
    // Core0 (Audio) reads from read buffer - no lock needed
    const ParameterBufferSet* readBuffer = readBuffer_.load();
    return readBuffer->tracks[static_cast<size_t>(id)].stepCount;
}

float ParameterManager::getValue(ParamId id, uint8_t stepIdx) const
{
    // Core0 (Audio) reads from read buffer - no lock needed
    const ParameterBufferSet* readBuffer = readBuffer_.load();
    return readBuffer->tracks[static_cast<size_t>(id)].getValue(stepIdx);
}

void ParameterManager::setValue(ParamId id, uint8_t stepIdx, float value)
{
    // Core1 (UI/Sequencer) writes to write buffer - no lock needed
    ParameterBufferSet* writeBuffer = writeBuffer_.load();

    // Apply clamping and rounding based on parameter definition
    const auto &paramDef = CORE_PARAMETERS[static_cast<size_t>(id)];
    float minVal = getFloatFromParameterValueType(paramDef.minValue);
    float maxVal = getFloatFromParameterValueType(paramDef.maxValue);

    float clampedValue = std::max(minVal, std::min(value, maxVal));

    if (paramDef.isBinary)
    { // For boolean parameters, round to 0 or 1
        clampedValue = (clampedValue > 0.5f) ? 1.0f : 0.0f;
    }
    else if (paramDef.minValue.index() == 0)
    { // If min value is int, assume integer parameter
        clampedValue = roundf(clampedValue);
    }

    // DEBUG: Trace parameter setting (only for Note parameter to reduce spam)
    /*
    if (id == ParamId::Note) {
        Serial.print("[PARAM SET DEBUG] Note step ");
        Serial.print(stepIdx);
        Serial.print(": ");
        Serial.print(value, 2);
        Serial.print(" -> ");
        Serial.print(clampedValue, 2);
        Serial.print(" (range: ");
        Serial.print(minVal, 2);
        Serial.print("-");
        Serial.print(maxVal, 2);
        Serial.println(")");
    }
    */

    writeBuffer->tracks[static_cast<size_t>(id)].setValue(stepIdx, clampedValue);

    // Note: Swing parameter is handled globally through GlobalParams, not as a step parameter
}

void ParameterManager::swapBuffers()
{
    // Atomically swap read and write buffer pointers
    // This is the only point where buffer assignments change
    ParameterBufferSet* currentRead = readBuffer_.load();
    ParameterBufferSet* currentWrite = writeBuffer_.load();

    // Swap the pointers atomically
    readBuffer_.store(currentWrite);
    writeBuffer_.store(currentRead);
}

void ParameterManager::randomizeParameters()
{
    // This operation affects both buffers, so we need synchronization
    uint32_t save = spin_lock_blocking(_lock);

    // Use a better random number generator
    static std::default_random_engine generator(std::chrono::system_clock::now().time_since_epoch().count());

    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i)
    {
        ParamId currentParamId = static_cast<ParamId>(i);

        // When randomizing, ensure the Slide parameter's length is set to max
        if (currentParamId == ParamId::Slide)
        {
            bufferA_.tracks[i].stepCount = 16;
            bufferB_.tracks[i].stepCount = 16;
        }

        const auto &paramDef = CORE_PARAMETERS[i];
        float minVal = getFloatFromParameterValueType(paramDef.minValue);
        float maxVal = getFloatFromParameterValueType(paramDef.maxValue);
        std::uniform_real_distribution<float> distribution(minVal, maxVal);

        // Get the current step count from either buffer (they should be the same)
        uint8_t stepCount = bufferA_.tracks[i].stepCount;

        for (uint8_t step = 0; step < stepCount; ++step)
        {
            // For slide and gate, we want a 10% chance of being 1, otherwise 0
            if (currentParamId == ParamId::Slide)
            {

                std::uniform_int_distribution<int> slide_dist(0, 12);
                int slide_value = (slide_dist(generator) == 0) ? 1 : 0;
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, slide_value);
                bufferB_.tracks[i].setValue(step, slide_value);
            }
            else if (currentParamId == ParamId::Gate)
            {
                if ((step % 2) == 0) // If the step is even
                {
                    // 75% chance of being 1, otherwise 0
                    std::uniform_int_distribution<int> gate_dist(0, 3);   // 0, 1, 2 for 1; 3 for 0
                    int gate_value = (gate_dist(generator) == 0) ? 0 : 1; // Corrected logic for 75%
                    // Set the same value in both buffers to maintain consistency
                    bufferA_.tracks[i].setValue(step, gate_value);
                    bufferB_.tracks[i].setValue(step, gate_value);
                }

                else
                {
                     std::uniform_int_distribution<int> gate_dist(0, 2);
                     int gate_value = (gate_dist(generator) == 0) ? 1 : 0;
                     // Set the same value in both buffers to maintain consistency
                     bufferA_.tracks[i].setValue(step, gate_value);
                     bufferB_.tracks[i].setValue(step, gate_value);
                }
            }
            else if (currentParamId == ParamId::GateSize)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.1f, 0.3f);
                float gateSizeValue = gate_size_dist(generator);
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, gateSizeValue);
                bufferB_.tracks[i].setValue(step, gateSizeValue);
            }
            else if (currentParamId == ParamId::Filter)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.2f, 0.7f);
                float gateSizeValue = gate_size_dist(generator);
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, gateSizeValue);
                bufferB_.tracks[i].setValue(step, gateSizeValue);
            }

            else if (currentParamId == ParamId::Attack)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.0f, 0.15f);
                float gateSizeValue = gate_size_dist(generator);
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, gateSizeValue);
                bufferB_.tracks[i].setValue(step, gateSizeValue);
            }

            else if (currentParamId == ParamId::Decay)
            {
                std::uniform_real_distribution<float> gate_size_dist(0.01f, 0.75f);
                float gateSizeValue = gate_size_dist(generator);
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, gateSizeValue);
                bufferB_.tracks[i].setValue(step, gateSizeValue);
            }
            else
            {
                float randomValue = distribution(generator);
                // Set the same value in both buffers to maintain consistency
                bufferA_.tracks[i].setValue(step, randomValue);
                bufferB_.tracks[i].setValue(step, randomValue);
            }
        }
    }

    spin_unlock(_lock, save);
}
