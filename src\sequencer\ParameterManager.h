#ifndef PARAMETER_MANAGER_H
#define PARAMETER_MANAGER_H

#include "SequencerDefs.h" // For ParamId, ParameterTrack, CORE_PARAMETERS, AS5600ParameterMode
#include "pico/sync.h"     // For spin_lock_t
#include <atomic>          // For atomic operations
#include <variant>         // For std::visit

// Helper function to safely get float from ParameterValueType variant
inline float getFloatFromParameterValueType(const ParameterValueType &v)
{
    return std::visit([](auto &&arg) -> float
                      {
                          using T = std::decay_t<decltype(arg)>;
                          if constexpr (std::is_same_v<T, float>)
                              return arg;
                          if constexpr (std::is_same_v<T, int>)
                              return static_cast<float>(arg);
                          if constexpr (std::is_same_v<T, bool>)
                              return arg ? 1.0f : 0.0f;
                          return 0.0f; // Fallback for unexpected types
                      },
                      v);
}

/**
 * @brief Double-buffered parameter storage for lock-free Core0/Core1 access
 *
 * Contains two identical sets of parameter tracks. Core0 (audio) reads from one buffer
 * while Core1 (UI/sequencer) writes to the other. Buffers are swapped atomically
 * at clock boundaries to eliminate race conditions and audio dropouts.
 */
struct ParameterBufferSet {
    ParameterTrack<SEQUENCER_MAX_STEPS> tracks[static_cast<size_t>(ParamId::Count)];

    void init() {
        for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
            tracks[i].init(getFloatFromParameterValueType(CORE_PARAMETERS[i].defaultValue));
        }
    }

    void copyFrom(const ParameterBufferSet& other) {
        for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
            tracks[i] = other.tracks[i];
        }
    }
};

/**
 * @brief Manages all parameter tracks for a sequencer with double-buffering for lock-free access.
 *
 * Uses two identical parameter buffer sets:
 * - Core0 (audio) reads from the "read buffer" without locks
 * - Core1 (UI/sequencer) writes to the "write buffer" without locks
 * - Buffers are swapped atomically at clock boundaries in advanceStep()
 *
 * This eliminates spin lock contention that could cause audio dropouts while
 * maintaining thread-safe parameter access and real-time recording functionality.
 */
class ParameterManager {
public:
    ParameterManager(); // Constructor to initialize buffers and atomic pointers

    /**
     * @brief Initializes all parameter tracks with their default values.
     */
    void init();

    // Core1 (UI/Sequencer) write methods - access write buffer
    void setStepCount(ParamId id, uint8_t steps);
    void setValue(ParamId id, uint8_t stepIdx, float value);
    void randomizeParameters();

    // Core0 (Audio) read methods - access read buffer
    uint8_t getStepCount(ParamId id) const;
    float getValue(ParamId id, uint8_t stepIdx) const;

    /**
     * @brief Atomically swap read/write buffers at clock boundaries
     *
     * Called from advanceStep() to ensure Core0 always reads from a stable buffer
     * while Core1 writes to the alternate buffer. This is the only point where
     * buffer pointers change, ensuring lock-free access.
     */
    void swapBuffers();

    /**
     * @brief Get direct read-only access to current read buffer
     *
     * Used by processStep() for efficient parameter access during step processing.
     * Only safe to call from Core0 (audio/clock) context.
     */
    const ParameterBufferSet* getReadBuffer() const { return readBuffer_.load(); }

    /**
     * @brief Get direct read-only access to current write buffer
     *
     * Used for real-time parameter feedback to read newly recorded values.
     * Only safe to call from Core1 (UI/sequencer) context after parameter recording.
     */
    const ParameterBufferSet* getWriteBuffer() const { return writeBuffer_.load(); }

private:
    // Double buffer storage
    ParameterBufferSet bufferA_;
    ParameterBufferSet bufferB_;

    // Atomic pointers for lock-free buffer swapping
    std::atomic<ParameterBufferSet*> readBuffer_;   // Core0 reads from this buffer
    std::atomic<ParameterBufferSet*> writeBuffer_;  // Core1 writes to this buffer

    // Fallback spin lock for operations that still need synchronization
    // (e.g., randomizeParameters which affects both buffers)
    mutable spin_lock_t* _lock;
};

#endif // PARAMETER_MANAGER_H