#ifndef UI_EVENT_HANDLER_H
#define UI_EVENT_HANDLER_H

#include <Arduino.h>
#include "../matrix/Matrix.h"
#include "../sequencer/SequencerDefs.h"
#include "../sensors/as5600.h"
#include "../LEDMatrix/LEDMatrixFeedback.h"
#include "ButtonManager.h"
#include "UIState.h"

// Forward declarations to prevent circular dependencies
class Sequencer;
class MidiNoteManager; // Forward declare MidiNoteManager

// =======================
//   CONSTANTS
// =======================
constexpr uint8_t NUMBER_OF_STEP_BUTTONS = 16;
constexpr unsigned long AS5600_DOUBLE_PRESS_WINDOW = 300; // ms
constexpr unsigned long CONTROL_LED_FLASH_DURATION_MS = 250;

// =======================
//   FUNCTION DECLARATIONS
// =======================

/**
 * @brief Initialize the UI event handler system.
 * @param UIState Reference to the central UI state object.
 */
void initUIEventHandler(UIState& UIState);

/**
 * @brief Main matrix event handler function.
 * @param evt Matrix button event.
 * @param UIState Reference to the central UI state object.
 * @param seq1 Reference to the first sequencer.
 * @param seq2 Reference to the second sequencer.
 * @param midiNoteManager Reference to the MIDI note manager.
 */
void matrixEventHandler(const MatrixButtonEvent &evt, UIState& UIState, Sequencer& seq1, Sequencer& seq2, MidiNoteManager& midiNoteManager);

#endif // UI_EVENT_HANDLER_H
