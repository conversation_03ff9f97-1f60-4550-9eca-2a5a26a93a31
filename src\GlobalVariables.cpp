#include "GlobalVariables.h"
#include "LEDMatrix/LEDMatrixFeedback.h"

/**
 * @file GlobalVariables.cpp
 * @brief Global variable definitions
 * 
 * This file defines global variables that are used throughout the codebase
 * for backward compatibility during the refactoring process.
 */

// =============================================================================
// GLOBAL VARIABLE DEFINITIONS
// =============================================================================

// Delay effect variables
float delayTarget = 0.0f;
float feedbackAmmount = 0.0f;

// Global parameters instance
GlobalParams globalParams;

// USB MIDI interface reference
midi::MidiInterface<midi::SerialMIDI<Adafruit_USBD_MIDI>>& usb_midi = app.getUSBMidi();

// Global variables for backward compatibility
uint8_t currentScale = 0;
bool isClockRunning = true;

// =============================================================================
// TOUCH SENSOR PRESENCE VARIABLES
// =============================================================================
// Global variables that reference the PicoMudrasApp instance
bool touchButtonsPresent = false;
bool touchMatrixPresent = false;

// =============================================================================
// ACCESSOR FUNCTION IMPLEMENTATIONS
// =============================================================================
// Forward declaration of the global app instance
extern PicoMudrasApp app;

bool getTouchButtonsPresent() {
    return app.getTouchButtonsPresent();
}

bool getTouchMatrixPresent() {
    return app.getTouchMatrixPresent();
}

// Wrapper function for setLEDTheme to bridge int parameter to LEDTheme enum
void setLEDTheme(int themeIndex) {
    setLEDTheme(static_cast<LEDTheme>(themeIndex));
}

// Clock control wrapper functions
void onClockStart() {
    app.onClockStart();
}

void onClockStop() {
    app.onClockStop();
}