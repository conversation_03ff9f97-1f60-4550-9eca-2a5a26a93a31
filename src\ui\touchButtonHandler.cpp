#include "touchButtonHandler.h"
#include <Adafruit_MPR121.h>
#include <cstring>
#include "UIEventHandler.h"
#include "../sequencer/Sequencer.h"
#include "../midi/MidiManager.h"
#include "../GlobalVariables.h"
#include "ButtonManager.h"
#include "../sensors/AS5600Manager.h"


// Touch button state arrays: 0-11 = touchButtons on MPR121 with address 0x5D
bool touchButtonState[kNumButtons] = {false};
bool prevTouchButtonState[kNumButtons] = {false};
unsigned long touchButtonPressTime[kNumButtons] = {0};

// External references
extern MidiNoteManager midiNoteManager;


extern void onClockStart();
extern void onClockStop();
extern bool isClockRunning;
extern uint8_t currentScale;
extern void setLEDTheme(int themeIndex);

void handleParameterMode(uint8_t buttonIndex, bool pressed, UIState &uiState);
void handleControlMode(uint8_t buttonIndex, bool pressed, UIState &uiState, Sequencer &seq1, Sequencer &seq2);

// Main handler for touch buttons
void handleTouchButtons(UIState& uiState, Sequencer& seq1, Sequencer& seq2)
{
    // Check if touch buttons are present
    if (!touchButtonsPresent) {
        // Zero out state arrays if sensor is absent to avoid spurious edges
        memset(touchButtonState, false, sizeof(touchButtonState));
        memset(prevTouchButtonState, false, sizeof(prevTouchButtonState));
        return;
    }

    // Get touch buttons sensor from global app instance
    extern PicoMudrasApp app;
    uint16_t touchedValueButtons = app.getTouchButtons().touched();

    bool seq1_button_pressed = false;
    bool seq2_button_pressed = false;

    // Process all 12 touch buttons (0-11)
    for (uint8_t i = 0; i < kNumButtons; i++)
    {
        bool currState = (touchedValueButtons & (1 << i));
        
        // Detect button press (rising edge)
        if (currState && !prevTouchButtonState[i])
        {
            touchButtonPressTime[i] = millis();
            
            // Track which sequencer buttons are pressed for mode switching
            if (i <= 5) seq1_button_pressed = true;      // Seq1 buttons (0-5)
            if (i >= 6 && i <= 11) seq2_button_pressed = true; // Seq2 buttons (6-11)
            
            // Handle button press based on current mode
            if (uiState.touchButtonMode == PARAMETER_MODE)
            {
                handleParameterMode(i, true, uiState);
            }
            else
            {
                handleControlMode(i, true, uiState, seq1, seq2);
            }
        }
        
        // Detect button release (falling edge)
        if (!currState && prevTouchButtonState[i])
        {
            unsigned long pressDuration = millis() - touchButtonPressTime[i];
            
            // Handle button release based on current mode
            if (uiState.touchButtonMode == PARAMETER_MODE)
            {
                handleParameterMode(i, false, uiState);
            }
            else
            {
                handleControlMode(i, false, uiState, seq1, seq2);
            }
        }

        // Update state arrays
        touchButtonState[i] = currState;
        prevTouchButtonState[i] = currState;
    }

    // Toggle mode if buttons from both sequencers are pressed simultaneously
    if (seq1_button_pressed && seq2_button_pressed)
    {
        uiState.touchButtonMode = (uiState.touchButtonMode == PARAMETER_MODE) ? CONTROL_MODE : PARAMETER_MODE;
        Serial.print("[TouchButtons] Mode switched to: ");
        Serial.println(uiState.touchButtonMode == PARAMETER_MODE ? "PARAMETER_MODE" : "CONTROL_MODE");
    }
}

// Handles button events in Parameter Mode
void handleParameterMode(uint8_t buttonIndex, bool pressed, UIState &uiState)
{
    if (buttonIndex <= 5)
    { 
        // Seq1 Parameter Buttons (0-5): Note, Velocity, Filter, Attack, Decay, Octave
        uiState.parameterButtonHeld[buttonIndex] = pressed;
        uiState.activeParameterSeq = pressed ? 1 : 0;
        
        if (pressed) {
            Serial.print("[Parameter Mode] Seq1 button ");
            Serial.print(buttonIndex);
            Serial.println(" pressed - recording parameter");
        }
    }
    else if (buttonIndex >= 6 && buttonIndex <= 11)
    { 
        // Seq2 Parameter Buttons (6-11): Note, Velocity, Filter, Attack, Decay, Octave
        uint8_t paramIndex = buttonIndex - 6; // Map 6-11 to 0-5
        uiState.parameterButtonHeld[paramIndex] = pressed;
        uiState.activeParameterSeq = pressed ? 2 : 0;
        
        if (pressed) {
            Serial.print("[Parameter Mode] Seq2 button ");
            Serial.print(buttonIndex);
            Serial.print(" (param ");
            Serial.print(paramIndex);
            Serial.println(") pressed - recording parameter");
        }
    }
}

// isLongPress function is now defined in ButtonManager.cpp

// Handles button events in Control Mode
void handleControlMode(uint8_t buttonIndex, bool pressed, UIState &uiState, Sequencer &seq1, Sequencer &seq2)
{
    if (pressed)
    { 
        // Button pressed - handle immediate actions
        switch (buttonIndex)
        {
            case BUTTON_PLAY_STOP:
                Serial.print("[Control Mode] Play/Stop button pressed - Current state: ");
                Serial.println(isClockRunning ? "RUNNING" : "STOPPED");
                if (isClockRunning) {
                    onClockStop();
                    Serial.println("[Control Mode] Clock stop requested");
                } else {
                    onClockStart();
                    Serial.println("[Control Mode] Clock start requested");
                }
                break;
                
            case BUTTON_CHANGE_SCALE:
                currentScale = (currentScale + 1) % 7;
                Serial.print("[Control Mode] Scale changed to: ");
                Serial.println(currentScale);
                break;
                
            case BUTTON_CHANGE_THEME:
                uiState.currentThemeIndex = (uiState.currentThemeIndex + 1) % 4;
                setLEDTheme(uiState.currentThemeIndex);
                Serial.print("[Control Mode] Theme changed to: ");
                Serial.println(uiState.currentThemeIndex);
                break;
                
            case BUTTON_SELECT_TEMPO:
                setControlTarget(uiState, AS5600ParameterMode::Tempo);
                Serial.println("[Control Mode] AS5600 set to control TEMPO");
                break;
                
            case BUTTON_SELECT_SHUFFLE:
                setControlTarget(uiState, AS5600ParameterMode::Shuffle);
                Serial.println("[Control Mode] AS5600 set to control SHUFFLE");
                break;
                
            case BUTTON_SELECT_DELAY_TIME:
                setControlTarget(uiState, AS5600ParameterMode::DelayTime);
                Serial.println("[Control Mode] AS5600 set to control DELAY_TIME");
                break;
                
            case BUTTON_SELECT_DELAY_FEEDBACK:
                setControlTarget(uiState, AS5600ParameterMode::DelayFeedback);
                Serial.println("[Control Mode] AS5600 set to control DELAY_FEEDBACK");
                break;
                
            case BUTTON_TOGGLE_DELAY:
                uiState.delayOn = !uiState.delayOn;
                Serial.print("[Control Mode] Delay toggled: ");
                Serial.println(uiState.delayOn ? "ON" : "OFF");
                break;
                
            case BUTTON_LFO1_ASSIGN:
                setControlTarget(uiState, AS5600ParameterMode::LFO1freq);
                Serial.println("[Control Mode] AS5600 set to control LFO1_FREQ");
                break;
                
            case BUTTON_LFO2_ASSIGN:
                setControlTarget(uiState, AS5600ParameterMode::LFO2freq);
                Serial.println("[Control Mode] AS5600 set to control LFO2_FREQ");
                break;
                
            case BUTTON_RANDOMIZE_SEQ1:
            case BUTTON_RANDOMIZE_SEQ2:
                // These are handled on button release to detect long press
                break;
                
            default:
                Serial.print("[Control Mode] Unknown button: ");
                Serial.println(buttonIndex);
                break;
        }
    }
    else
    { 
        // Button released - handle actions that depend on press duration
        unsigned long pressDuration = millis() - touchButtonPressTime[buttonIndex];
        
        if (buttonIndex == BUTTON_RANDOMIZE_SEQ1)
        {
            if (isLongPress(pressDuration))
            {
                seq1.reset();
                Serial.println("[Control Mode] Seq1 RESET (long press)");
            }
            else
            {
                seq1.randomizeParameters();
                Serial.println("[Control Mode] Seq1 RANDOMIZED (short press)");
            }
        }
        else if (buttonIndex == BUTTON_RANDOMIZE_SEQ2)
        {
            if (isLongPress(pressDuration))
            {
                seq2.reset();
                Serial.println("[Control Mode] Seq2 RESET (long press)");
            }
            else
            {
                seq2.randomizeParameters();
                Serial.println("[Control Mode] Seq2 RANDOMIZED (short press)");
            }
        }
    }
}
