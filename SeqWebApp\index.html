<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Interactive polymetric sequencer visualizer for the PicoMudras project with real-time parameter control and modern glass-morphism UI">
    <meta name="keywords" content="sequencer, polymetric, music, visualization, PicoMudras, interactive">
    <meta name="author" content="PicoMudras Project">
    <title>PicoMudras Polymetric Sequencer Visualizer</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
</head>
<body>
    <div class="container">
        <header role="banner">
            <h1>PicoMudras Polymetric Sequencer</h1>
            <p>Interactive visualization of independent parameter tracks</p>
        </header>

        <main role="main">
            <section class="controls" aria-label="Transport and Tempo Controls">
                <div class="transport-controls" role="group" aria-label="Transport Controls">
                    <button id="playBtn" class="btn btn-primary" aria-label="Play or pause sequencer" aria-pressed="false">
                        Play
                    </button>
                    <button id="stopBtn" class="btn btn-secondary" aria-label="Stop sequencer">
                        Stop
                    </button>
                    <button id="resetBtn" class="btn btn-secondary" aria-label="Reset sequencer to beginning">
                        Reset
                    </button>
                </div>

                <div class="tempo-control" role="group" aria-label="Tempo Control">
                    <label for="tempoSlider">Tempo: <span id="tempoValue" aria-live="polite">120</span> BPM</label>
                    <input type="range" id="tempoSlider" min="60" max="200" value="120" class="slider"
                           aria-label="Adjust tempo from 60 to 200 BPM" aria-valuemin="60" aria-valuemax="200" aria-valuenow="120">
                </div>
            </section>
        
            <section class="sequencer-info" aria-label="Sequencer Status Information">
                <div class="info-panel" role="region" aria-labelledby="pattern-info-title">
                    <h3 id="pattern-info-title">Pattern Info</h3>
                    <div class="info-item">
                        <span>Current Step:</span>
                        <span id="currentStep" aria-live="polite" aria-label="Current step number">1</span>
                    </div>
                    <div class="info-item">
                        <span>Pattern Length:</span>
                        <span id="patternLength" aria-live="polite" aria-label="Total pattern length">16</span>
                    </div>
                    <div class="info-item">
                        <span>Full Cycle:</span>
                        <span id="fullCycle" aria-live="polite" aria-label="Full cycle length">16</span> steps
                    </div>
                </div>
            </section>
        
            <section class="parameter-controls" aria-label="Parameter Track Length Controls">
                <h3 id="param-controls-title">Parameter Track Lengths</h3>
                <div class="param-grid" role="group" aria-labelledby="param-controls-title">
                    <div class="param-control">
                        <label for="noteSlider">Note: <span id="noteSteps" aria-live="polite">16</span></label>
                        <input type="range" id="noteSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Note parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="velSlider">Velocity: <span id="velSteps" aria-live="polite">16</span></label>
                        <input type="range" id="velSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Velocity parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="filterSlider">Filter: <span id="filterSteps" aria-live="polite">16</span></label>
                        <input type="range" id="filterSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Filter parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="attackSlider">Attack: <span id="attackSteps" aria-live="polite">16</span></label>
                        <input type="range" id="attackSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Attack parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="decaySlider">Decay: <span id="decaySteps" aria-live="polite">16</span></label>
                        <input type="range" id="decaySlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Decay parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="octSlider">Octave: <span id="octSteps" aria-live="polite">16</span></label>
                        <input type="range" id="octSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Octave parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="gateSizeSlider">Gate Length: <span id="gateSizeSteps" aria-live="polite">16</span></label>
                        <input type="range" id="gateSizeSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Gate length parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="gateSlider">Gate: <span id="gateSteps" aria-live="polite">16</span></label>
                        <input type="range" id="gateSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Gate parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                    <div class="param-control">
                        <label for="slideSlider">Slide: <span id="slideSteps" aria-live="polite">16</span></label>
                        <input type="range" id="slideSlider" min="1" max="16" value="16" class="param-slider"
                               aria-label="Slide parameter track length" aria-valuemin="1" aria-valuemax="16" aria-valuenow="16">
                    </div>
                </div>
            </section>
        
            <section class="visualization-container" aria-label="Sequencer Visualization">
                <div id="p5-container" role="img" aria-label="Interactive sequencer visualization showing parameter tracks and current playback position"></div>
            </section>

            <section class="pattern-presets" aria-label="Pattern Preset Controls">
                <h3 id="presets-title">Pattern Presets</h3>
                <div class="preset-buttons" role="group" aria-labelledby="presets-title">
                    <button class="btn btn-preset" onclick="loadPreset('basic')"
                            aria-label="Load basic 4/4 pattern preset">
                        Basic 4/4
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('polyrhythm')"
                            aria-label="Load polyrhythmic pattern preset">
                        Polyrhythm
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('complex')"
                            aria-label="Load complex polyrhythmic pattern preset">
                        Complex
                    </button>
                    <button class="btn btn-preset" onclick="loadPreset('random')"
                            aria-label="Load random pattern preset">
                        Random
                    </button>
                </div>
            </section>
        </main>

        <footer role="contentinfo" style="text-align: center; padding: 2rem 0; color: var(--text-muted); font-size: 0.9rem;">
            <p>PicoMudras Polymetric Sequencer Visualizer | Use keyboard shortcuts: Space (Play/Pause), R (Reset), 1-4 (Presets)</p>
        </footer>
    </div>

    <script src="sequencer.js"></script>
</body>
</html>