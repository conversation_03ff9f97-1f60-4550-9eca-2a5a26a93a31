<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PicoMudras Polymetric Sequencer Visualizer</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>PicoMudras Polymetric Sequencer</h1>
            <p>Interactive visualization of independent parameter tracks</p>
        </header>
        
        <div class="controls">
            <div class="transport-controls">
                <button id="playBtn" class="btn btn-primary">Play</button>
                <button id="stopBtn" class="btn btn-secondary">Stop</button>
                <button id="resetBtn" class="btn btn-secondary">Reset</button>
            </div>
            
            <div class="tempo-control">
                <label for="tempoSlider">Tempo: <span id="tempoValue">120</span> BPM</label>
                <input type="range" id="tempoSlider" min="60" max="200" value="120" class="slider">
            </div>
        </div>
        
        <div class="sequencer-info">
            <div class="info-panel">
                <h3>Pattern Info</h3>
                <div class="info-item">
                    <span>Current Step:</span>
                    <span id="currentStep">1</span>
                </div>
                <div class="info-item">
                    <span>Pattern Length:</span>
                    <span id="patternLength">16</span>
                </div>
                <div class="info-item">
                    <span>Full Cycle:</span>
                    <span id="fullCycle">16</span> steps
                </div>
            </div>
        </div>
        
        <div class="parameter-controls">
            <h3>Parameter Track Lengths</h3>
            <div class="param-grid">
                <div class="param-control">
                    <label>Note: <span id="noteSteps">16</span></label>
                    <input type="range" id="noteSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Velocity: <span id="velSteps">16</span></label>
                    <input type="range" id="velSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Filter: <span id="filterSteps">16</span></label>
                    <input type="range" id="filterSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Attack: <span id="attackSteps">16</span></label>
                    <input type="range" id="attackSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Decay: <span id="decaySteps">16</span></label>
                    <input type="range" id="decaySlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Octave: <span id="octSteps">16</span></label>
                    <input type="range" id="octSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Gate Length: <span id="gateSizeSteps">16</span></label>
                    <input type="range" id="gateSizeSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Gate: <span id="gateSteps">16</span></label>
                    <input type="range" id="gateSlider" min="1" max="16" value="16" class="param-slider">
                </div>
                <div class="param-control">
                    <label>Slide: <span id="slideSteps">16</span></label>
                    <input type="range" id="slideSlider" min="1" max="16" value="16" class="param-slider">
                </div>
            </div>
        </div>
        
        <div id="p5-container"></div>
        
        <div class="pattern-presets">
            <h3>Pattern Presets</h3>
            <div class="preset-buttons">
                <button class="btn btn-preset" onclick="loadPreset('basic')">Basic 4/4</button>
                <button class="btn btn-preset" onclick="loadPreset('polyrhythm')">Polyrhythm</button>
                <button class="btn btn-preset" onclick="loadPreset('complex')">Complex</button>
                <button class="btn btn-preset" onclick="loadPreset('random')">Random</button>
            </div>
        </div>
    </div>
    
    <script src="sequencer.js"></script>
</body>
</html>