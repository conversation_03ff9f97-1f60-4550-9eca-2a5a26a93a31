#include "SynthEngine.h"
#include <cmath>

// Scale definitions (moved from PicoMudrasApp)
const int SynthEngine::scale_[7][48] = {
    // Major scale (Ionian)
    {0, 2, 4, 5, 7, 9, 11, 12, 14, 16, 17, 19, 21, 23, 24, 26, 28, 29, 31, 33, 35, 36, 38, 40, 41, 43, 45, 47, 48, 50, 52, 53, 55, 57, 59, 60, 62, 64, 65, 67, 69, 71, 72, 74, 76, 77, 79, 81},
    // <PERSON>
    {0, 2, 3, 5, 7, 9, 10, 12, 14, 15, 17, 19, 21, 22, 24, 26, 27, 29, 31, 33, 34, 36, 38, 39, 41, 43, 45, 46, 48, 50, 51, 53, 55, 57, 58, 60, 62, 63, 65, 67, 69, 70, 72, 74, 75, 77, 79, 81},
    // Ph<PERSON>gian
    {0, 1, 3, 5, 7, 8, 10, 12, 13, 15, 17, 19, 20, 22, 24, 25, 27, 29, 31, 32, 34, 36, 37, 39, 41, 43, 44, 46, 48, 49, 51, 53, 55, 56, 58, 60, 61, 63, 65, 67, 68, 70, 72, 73, 75, 77, 79, 80},
    // Lydian
    {0, 2, 4, 6, 7, 9, 11, 12, 14, 16, 18, 19, 21, 23, 24, 26, 28, 30, 31, 33, 35, 36, 38, 40, 42, 43, 45, 47, 48, 50, 52, 54, 55, 57, 59, 60, 62, 64, 66, 67, 69, 71, 72, 74, 76, 78, 79, 81},
    // Mixolydian
    {0, 2, 4, 5, 7, 9, 10, 12, 14, 16, 17, 19, 21, 22, 24, 26, 28, 29, 31, 33, 34, 36, 38, 40, 41, 43, 45, 46, 48, 50, 52, 53, 55, 57, 58, 60, 62, 64, 65, 67, 69, 70, 72, 74, 76, 77, 79, 81},
    // Natural Minor (Aeolian)
    {0, 2, 3, 5, 7, 8, 10, 12, 14, 15, 17, 19, 20, 22, 24, 26, 27, 29, 31, 32, 34, 36, 38, 39, 41, 43, 44, 46, 48, 50, 51, 53, 55, 56, 58, 60, 62, 63, 65, 67, 68, 70, 72, 74, 75, 77, 79, 80},
    // Locrian
    {0, 1, 3, 5, 6, 8, 10, 12, 13, 15, 17, 18, 20, 22, 24, 25, 27, 29, 30, 32, 34, 36, 37, 39, 41, 42, 44, 46, 48, 49, 51, 53, 54, 56, 58, 60, 61, 63, 65, 66, 68, 70, 72, 73, 75, 77, 78, 80}
};

SynthEngine::SynthEngine() {
    // Initialize voice states with default values
    voiceState1_ = VoiceState();
    voiceState2_ = VoiceState();
    globalParams_ = GlobalParams();
}

SynthEngine::~SynthEngine() {
    // Cleanup if needed
}

void SynthEngine::initialize(float sampleRate) {
    sampleRate_ = sampleRate;
    
    // Initialize oscillators
    initializeOscillators();
    
    // Initialize filters
    filt1_.Init(sampleRate_);
    filt2_.Init(sampleRate_);
    highPass1_.Init(sampleRate_);
    highPass2_.Init(sampleRate_);
    delLowPass_.Init(sampleRate_);
    
    // Initialize envelopes
    env1_.Init(sampleRate_);
    env2_.Init(sampleRate_);
    
    // Initialize delay line
    del1_.Init();
    del1_.SetDelay(currentDelay_);
    
    // Initialize LFOs
    lfo1_.Init(sampleRate_);
    lfo1_.SetWaveform(daisysp::Oscillator::WAVE_SIN);
    lfo1_.SetFreq(globalParams_.lfo1freq);
    lfo1_.SetAmp(globalParams_.lfo1amp);
    
    lfo2_.Init(sampleRate_);
    lfo2_.SetWaveform(daisysp::Oscillator::WAVE_SIN);
    lfo2_.SetFreq(globalParams_.lfo2freq);
    lfo2_.SetAmp(globalParams_.lfo2amp);
    
    // Set filter types
    highPass1_.SetFreq(80.0f);
    highPass1_.SetRes(0.1f);
    highPass2_.SetFreq(80.0f);
    highPass2_.SetRes(0.1f);
    
    delLowPass_.SetFreq(8000.0f);
    delLowPass_.SetRes(0.1f);
}

void SynthEngine::initializeOscillators() {
    // Initialize voice 1 oscillators
    osc1A_.Init(sampleRate_);
    osc1A_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SAW);
    osc1A_.SetAmp(0.3f);
    
    osc1B_.Init(sampleRate_);
    osc1B_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SAW);
    osc1B_.SetAmp(0.3f);
    
    osc1C_.Init(sampleRate_);
    osc1C_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SQUARE);
    osc1C_.SetAmp(0.2f);
    
    // Initialize voice 2 oscillators
    osc2A_.Init(sampleRate_);
    osc2A_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SAW);
    osc2A_.SetAmp(0.3f);
    
    osc2B_.Init(sampleRate_);
    osc2B_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SAW);
    osc2B_.SetAmp(0.3f);
    
    osc2C_.Init(sampleRate_);
    osc2C_.SetWaveform(daisysp::Oscillator::WAVE_POLYBLEP_SQUARE);
    osc2C_.SetAmp(0.2f);
}

void SynthEngine::processAudio(audio_buffer_t *buffer) {
    if (!buffer) return;
    
    int16_t *samples = (int16_t *)buffer->buffer->bytes;
    
    // Update frequency slewing for slide effects
    updateFrequencySlewing();
    
    // Process LFOs
    lfo1LEDWaveformValue_ = lfo1_.Process();
    lfo2LEDWaveformValue_ = lfo2_.Process();
    
    // Process each sample
    for (uint i = 0; i < buffer->sample_count; i++) {
        float leftSample = 0.0f;
        float rightSample = 0.0f;
        
        // Process voice 1
        if (gate1_) {
            processVoice(0, &leftSample, &rightSample);
        }
        
        // Process voice 2
        if (gate2_) {
            processVoice(1, &leftSample, &rightSample);
        }
        
        // Apply global effects
        processAudioEffects(leftSample, rightSample);
        
        // Convert to 16-bit and store
        samples[i * 2] = (int16_t)(leftSample * INT16_MAX_AS_FLOAT);
        samples[i * 2 + 1] = (int16_t)(rightSample * INT16_MAX_AS_FLOAT);
    }
    
    buffer->sample_count = buffer->sample_count;
}

void SynthEngine::processVoice(int voiceIndex, float *leftSample, float *rightSample) {
    if (voiceIndex < 0 || voiceIndex > 1) return;
    
    const VoiceState &voice = (voiceIndex == 0) ? voiceState1_ : voiceState2_;
    daisysp::Adsr &env = (voiceIndex == 0) ? env1_ : env2_;
    daisysp::LadderFilter &filter = (voiceIndex == 0) ? filt1_ : filt2_;
    
    // Get oscillator references based on voice
    daisysp::Oscillator *oscA, *oscB, *oscC;
    if (voiceIndex == 0) {
        oscA = &osc1A_;
        oscB = &osc1B_;
        oscC = &osc1C_;
    } else {
        oscA = &osc2A_;
        oscB = &osc2B_;
        oscC = &osc2C_;
    }
    
    // Calculate frequency from note and octave
    float frequency = noteToFrequency(voice.note, voice.Oct);
    
    // Apply slide if enabled
    if (voice.slide) {
        frequency = freqSlew_[voiceIndex][0].currentFreq;
    }
    
    // Set oscillator frequencies with slight detuning
    oscA->SetFreq(frequency);
    oscB->SetFreq(frequency * OSC_DETUNE_FACTOR);
    oscC->SetFreq(frequency * 0.5f); // Sub oscillator
    
    // Process oscillators
    float oscOutput = oscA->Process() + oscB->Process() + oscC->Process();
    
    // Apply envelope
    float envOutput = env.Process(gate1_ || gate2_);
    oscOutput *= envOutput * voice.Vel;
    
    // Apply filter
    float filterFreq = calculateFilterFrequency(voice.filter);
    filter.SetFreq(filterFreq);
    filter.SetRes(0.7f);
    oscOutput = filter.Process(oscOutput);
    
    // Apply high-pass filter
    daisysp::Svf &highPass = (voiceIndex == 0) ? highPass1_ : highPass2_;
    highPass.Process(oscOutput);
    oscOutput = highPass.High();
    
    // Add to output samples
    *leftSample += oscOutput;
    *rightSample += oscOutput;
}

void SynthEngine::processAudioEffects(float &sample1, float &sample2) {
    // Process delay
    float delayInput = (sample1 + sample2) * 0.5f;
    
    // Update delay time smoothing
    float targetDelay = globalParams_.delayTime * sampleRate_;
    currentDelay_ = delayTimeSmoothing(currentDelay_, targetDelay, 0.001f);
    del1_.SetDelay(currentDelay_);
    
    // Process delay line
    float delayOutput = del1_.Read();
    delLowPass_.Process(delayOutput);
    delayOutput = delLowPass_.Low();
    
    // Apply feedback
    float feedbackSample = delayInput + (delayOutput * globalParams_.delayFeedback);
    del1_.Write(feedbackSample);
    
    // Mix delay output
    float delayMix = globalParams_.delayTime;
    sample1 += delayOutput * delayMix;
    sample2 += delayOutput * delayMix;
    
    // Apply soft clipping
    sample1 = tanhf(sample1 * 0.7f);
    sample2 = tanhf(sample2 * 0.7f);
}

void SynthEngine::updateFrequencySlewing() {
    for (int voice = 0; voice < 2; voice++) {
        for (int osc = 0; osc < 3; osc++) {
            SlewParams &slew = freqSlew_[voice][osc];
            float diff = slew.targetFreq - slew.currentFreq;
            slew.currentFreq += diff * FREQ_SLEW_RATE;
        }
    }
}

float SynthEngine::noteToFrequency(float note, float octave) {
    // Convert note index to MIDI note number
    int noteIndex = (int)note;
    if (noteIndex < 0 || noteIndex >= 48) {
        noteIndex = 0;
    }
    
    // Get semitone from scale
    int semitone = scale_[currentScale_][noteIndex];
    
    // Apply octave offset
    int octaveOffset = (int)(octave * 4.0f); // Map 0-1 to 0-4 octaves
    semitone += octaveOffset * 12;
    
    // Convert to frequency
    return baseFreq_ * powf(2.0f, semitone / 12.0f);
}

float SynthEngine::calculateFilterFrequency(float filterValue) {
    // Map 0-1 to 80Hz - 8000Hz logarithmically
    float minFreq = 80.0f;
    float maxFreq = 8000.0f;
    return minFreq * powf(maxFreq / minFreq, filterValue);
}

void SynthEngine::applyEnvelopeParameters(const VoiceState &state, daisysp::Adsr &env, int voiceNum) {
    env.SetTime(daisysp::ADSR_SEG_ATTACK, state.attack);
    env.SetTime(daisysp::ADSR_SEG_DECAY, state.decay);
    env.SetTime(daisysp::ADSR_SEG_RELEASE, state.decay); // Use decay for release
    env.SetSustainLevel(0.7f);
}

float SynthEngine::delayTimeSmoothing(float currentDelay, float targetDelay, float slewRate) {
    float difference = targetDelay - currentDelay;
    return currentDelay + (difference * slewRate);
}

void SynthEngine::updateVoiceState(const VoiceState &voice1, const VoiceState &voice2) {
    voiceState1_ = voice1;
    voiceState2_ = voice2;
    
    // Apply envelope parameters
    applyEnvelopeParameters(voiceState1_, env1_, 0);
    applyEnvelopeParameters(voiceState2_, env2_, 1);
    
    // Update slide targets if slide is enabled
    if (voiceState1_.slide) {
        float freq = noteToFrequency(voiceState1_.note, voiceState1_.Oct);
        freqSlew_[0][0].targetFreq = freq;
        freqSlew_[0][1].targetFreq = freq * OSC_DETUNE_FACTOR;
        freqSlew_[0][2].targetFreq = freq * 0.5f;
    }
    
    if (voiceState2_.slide) {
        float freq = noteToFrequency(voiceState2_.note, voiceState2_.Oct);
        freqSlew_[1][0].targetFreq = freq;
        freqSlew_[1][1].targetFreq = freq * OSC_DETUNE_FACTOR;
        freqSlew_[1][2].targetFreq = freq * 0.5f;
    }
}

void SynthEngine::updateGlobalParams(const GlobalParams &params) {
    globalParams_ = params;
    
    // Update LFO parameters
    lfo1_.SetFreq(globalParams_.lfo1freq);
    lfo1_.SetAmp(globalParams_.lfo1amp);
    lfo2_.SetFreq(globalParams_.lfo2freq);
    lfo2_.SetAmp(globalParams_.lfo2amp);
}

void SynthEngine::setGate(int voiceIndex, bool gateState) {
    if (voiceIndex == 0) {
        gate1_ = gateState;
        if (gateState) {
            env1_.Retrigger(false);
        }
    } else if (voiceIndex == 1) {
        gate2_ = gateState;
        if (gateState) {
            env2_.Retrigger(false);
        }
    }
}