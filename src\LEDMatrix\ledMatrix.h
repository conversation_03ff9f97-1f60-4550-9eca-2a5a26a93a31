#ifndef LEDMATRIX_H
#define LEDMATRIX_H

#include <Arduino.h>
#include <FastLED.h>
#include "../HardwareConfig.h"

/**
 * @class LEDMatrix
 * @brief Controls an 8x8 WS2812B LED matrix.
 */
class LEDMatrix {
public:
    static constexpr uint8_t WIDTH = LED_MATRIX_WIDTH;
    static constexpr uint8_t HEIGHT = LED_MATRIX_HEIGHT;
    static constexpr uint8_t DATA_PIN = LED_MATRIX_DATA_PIN;

    

    LEDMatrix();
    void begin(uint8_t brightness = 200);
    void setLED(int x, int y, const CRGB& color);
    void setAll(const CRGB& color);
    void show();
    void clear();

    // Optional: direct access for advanced use
    CRGB* getLeds();

private:
    CRGB leds[WIDTH * HEIGHT];
};

#endif // LEDMATRIX_H
