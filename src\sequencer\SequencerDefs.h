#ifndef SEQUENCER_DEFS_H
#define SEQUENCER_DEFS_H

#include <stdint.h>
#include <variant> // Required for std::variant
#include "../HardwareConfig.h"

constexpr uint16_t PULSES_PER_QUARTER_NOTE = 480;
constexpr uint8_t PULSES_PER_SEQUENCER_STEP = PULSES_PER_QUARTER_NOTE / 4;
// SEQUENCER_MAX_STEPS is defined in HardwareConfig.h
constexpr uint8_t MIN_STEPS = 2;
constexpr uint8_t DEFAULT_STEPS = 16;

// --- Global Parameters ---
// These parameters affect the entire sequencer/synth globally.
struct GlobalParams {
    float tempo = 120.0f;
    float swing = 0.5f; // Renamed from shuffle for clarity; 0.5 = no swing
    float delayTime = 0.0f;
    float delayFeedback = 0.0f;
    float lfo1freq = 0.1f;
    float lfo1amp = 0.0f;
    float lfo2freq = 0.1f;
    float lfo2amp = 0.0f;
};

// Declare a global instance, to be defined in the main .ino file
extern GlobalParams globalParams;

// --- Per-Step Parameters ---
// These parameters can have different values for each step in a sequence.
enum class ParamId : uint8_t
{
    Note,
    Vel,
    Filter,
    Attack,
    Decay,
    Oct,
    GateSize,
    Gate,
    Slide,
    Count
};

// Constant for array sizing based on ParamId::Count
constexpr uint8_t PARAM_ID_COUNT = static_cast<uint8_t>(ParamId::Count);

// --- UI and Control Enums ---

// Defines which parameter is currently under control by the AS5600 sensor.
enum class AS5600ParameterMode : uint8_t
{
    Vel = 0,
    Filter = 1,
    Attack = 2,
    Decay = 3,
    DelayTime = 4,
    DelayFeedback = 5,
    LFO1freq = 6,
    LFO1amp = 7,
    LFO2freq = 8,
    LFO2amp = 9,
    Tempo = 10,
    Shuffle = 11, // UI name, corresponds to globalParams.swing
    COUNT = 12
};

// AS5600 parameter base values (per voice) - for parameters that can be modulated.
struct AS5600BaseValues
{
    float Vel = 0.0f;
    float filter = 0.0f;
    float attack = 0.0f;
    float decay = 0.0f;
    // Global params like delay/LFO are not stored here
};

// Define StepEditButtons struct for step parameter edit state (6 buttons)
struct StepEditButtons
{
    bool note;
    bool Vel;
    bool filter;
    bool attack;
    bool decay;
    bool Oct;
};

// Fixed-size parameter value array
template <uint8_t SIZE>
struct ParameterTrack
{
    float values[SIZE];
    uint8_t stepCount;
    float defaultValue;

    void init(float defValue)
    {
        defaultValue = defValue;
        stepCount = DEFAULT_STEPS;
        for (uint8_t i = 0; i < SIZE; ++i)
        {
            values[i] = defValue;
        }
    }

    float getValue(uint8_t stepIdx) const
    {
        if (stepCount == 0) return defaultValue;
        return values[stepIdx % stepCount];
    }

    void setValue(uint8_t stepIdx, float value)
    {
        if (stepCount == 0) return;
        values[stepIdx % stepCount] = value;
    }

    void resize(uint8_t newStepCount)
    {
        if (newStepCount >= MIN_STEPS && newStepCount <= SIZE)
        {
            if (newStepCount > stepCount)
            {
                for (uint8_t i = stepCount; i < newStepCount; ++i)
                {
                    values[i] = defaultValue;
                }
            }
            stepCount = newStepCount;
        }
    }
};

using ParameterValueType = std::variant<int, float, bool>;

struct ParameterDefinition
{
    const char *name;
    ParameterValueType defaultValue;
    ParameterValueType minValue;
    ParameterValueType maxValue;
    bool isBinary;
    uint8_t defaultSteps;
};

constexpr ParameterDefinition CORE_PARAMETERS[] = {
    {"Note", 0.f, 0.f, 21.f, false, DEFAULT_STEPS},
    {"Vel", 0.5f, 0.0f, 1.0f, false, DEFAULT_STEPS},
    {"Filter", 0.5f, 0.0f, 1.0f, false, DEFAULT_STEPS},
    {"Attack", 0.005f, 0.0f, 1.f, false, DEFAULT_STEPS},
    {"Decay", 0.11f, 0.f, 1.0f, false, DEFAULT_STEPS},
    {"Oct", 0.0f, 0.f, 1.0f, false, DEFAULT_STEPS},
    {"GateSize", 0.4f, 0.01f, 1.0f, false, DEFAULT_STEPS},
    {"Gate", false, false, true, true, DEFAULT_STEPS},
    {"Slide", false, false, true, true, DEFAULT_STEPS},
};

struct VoiceState
{
    float note = 0.0f;
    float Vel = 0.8f;
    float filter = 0.6f;
    float attack = 0.01f;
    float decay = 0.11f;
    float Oct = 0.f;
    uint16_t GateSize = PULSES_PER_SEQUENCER_STEP / 2;
    bool gate = true;
    bool slide = false;
    bool retrigger = false;
};

struct Step
{
    float note = 0.0f;
    float Vel = 0.5f;
    float filter;
    float attack = 0.04f;
    float decay = 0.1f;
    float Oct = 0.f;
    uint16_t GateSize = PULSES_PER_SEQUENCER_STEP / 2;
    bool gate = false;
    bool slide = false;
};

struct GateTimer
{
    volatile bool active = false;
    volatile uint16_t remainingTicks = 0;
    volatile uint32_t totalTicksProcessed = 0;

    void start(uint16_t durationTicks) volatile
    {
        active = true;
        remainingTicks = durationTicks;
        totalTicksProcessed = 0;
    }

    void tick() volatile
    {
        totalTicksProcessed++;
        if (active && remainingTicks > 0)
        {
            remainingTicks--;
            if (remainingTicks == 0)
            {
                active = false;
            }
        }
    }

    void stop() volatile
    {
        active = false;
        remainingTicks = 0;
    }

    bool isExpired() const volatile
    {
        return !active && remainingTicks == 0;
    }
};

float mapNormalizedValueToParamRange(ParamId id, float normalizedValue);

#endif // SEQUENCER_DEFS_H