#include "Matrix.h"
#include "Arduino.h"

// --- Matrix Mapping Definitions ---
// Define the mapping of physical matrix rows to MPR121 electrode inputs.
// Pin mappings are now centralized in HardwareConfig.h
const uint8_t MATRIX_ROW_INPUTS[TOUCH_MATRIX_ROWS] = TOUCH_MATRIX_ROW_PINS;
// Define the mapping of physical matrix columns to MPR121 electrode inputs.
const uint8_t MATRIX_COL_INPUTS[TOUCH_MATRIX_COLS] = TOUCH_MATRIX_COL_PINS;

// Array to store the mapping of each button index to its corresponding row and column inputs.
static MatrixButton matrixButtons[MATRIX_BUTTON_COUNT];
// Array to store the current state (pressed/released) of each button.
static bool buttonState[MATRIX_BUTTON_COUNT];
// Pointer to the Adafruit_MPR121 sensor instance.
static Adafruit_MPR121 *mpr121 = nullptr;
// Function pointer for the generic button event handler (pressed or released).
static void (*eventHandler)(const MatrixButtonEvent &) = nullptr;
// Function pointer for the rising edge (button press) specific handler.
static void (*risingEdgeHandler)(uint8_t buttonIndex) = nullptr;

// Sets up the mapping between linear button indices and matrix row/column inputs.
static void setupMatrixMapping() {
    uint8_t idx = 0;
    // Iterate through each row and column to populate the matrixButtons array.
    for (uint8_t row = 0; row < TOUCH_MATRIX_ROWS; ++row) {
        for (uint8_t col = 0; col < TOUCH_MATRIX_COLS; ++col) {
            // Assign the corresponding MPR121 input pins for the current row and column.
            matrixButtons[idx].rowInput = MATRIX_ROW_INPUTS[row];
            matrixButtons[idx].colInput = MATRIX_COL_INPUTS[col];
            ++idx; // Move to the next button index.
        }
    }
}

// Scans a single matrix button to determine its state based on the touch bits from the MPR121.
// A button is considered pressed if both its row and column inputs are touched.
// Tip: This logic assumes a simple matrix where row and column inputs are directly sensed.
// For more complex matrices or sensors, the scan logic might differ.
static bool scanMatrixButton(const MatrixButton &btn, uint16_t touchBits) {
    return (touchBits & (1 << btn.rowInput)) &&
           (touchBits & (1 << btn.colInput));
}

// Updates the state of all buttons based on the latest touch bits from the sensor.
// It also triggers event handlers if a button's state changes.
// Tip: Consider adding debouncing logic here to prevent multiple events for a single press/release.
// Tip: For performance-critical applications, optimize the loop or consider interrupt-driven scanning if the sensor supports it.
static void updateButtonStates(uint16_t touchBits) {
    for (uint8_t i = 0; i < MATRIX_BUTTON_COUNT; ++i) {
        bool prev = buttonState[i]; // Get the previous state of the button.
        bool curr = scanMatrixButton(matrixButtons[i], touchBits); // Get the current state.
        // Check if the button state has changed.
        if (curr != prev) {
            buttonState[i] = curr; // Update the button state.
            // If the button is now pressed and a rising edge handler is set, call it.
            if (curr && risingEdgeHandler) {
                risingEdgeHandler(i);
            }
            // If a generic event handler is set, call it with the button event details.
            if (eventHandler) {
                MatrixButtonEvent evt;
                evt.buttonIndex = i;
                evt.type = curr ? MATRIX_BUTTON_PRESSED : MATRIX_BUTTON_RELEASED;
                eventHandler(evt);
            }
        }
    }
}

// Initializes the Matrix module.
// Assigns the MPR121 sensor instance and sets up the button mapping.
// Initializes all button states to false (not pressed).
// Tip: Add error handling to check if the sensor initialization was successful before proceeding.
void Matrix_init(Adafruit_MPR121 *sensor) {
    mpr121 = sensor;
    setupMatrixMapping();
    for (uint8_t i = 0; i < MATRIX_BUTTON_COUNT; ++i) {
        buttonState[i] = false;
    }
    eventHandler = nullptr; // Initialize event handlers to null.
    risingEdgeHandler = nullptr;
}

// Scans the matrix for button presses and updates the button states.
// This function should be called periodically in the main loop.
// Tip: The frequency of calling this function affects responsiveness and CPU usage.
// Tip: Add error handling for the mpr121->touched() call in case of sensor communication issues.
void Matrix_scan() {
    if (!mpr121) // Check if the sensor is initialized.
        return;
    uint16_t touchBits = mpr121->touched(); // Read the touch status from the sensor.
    updateButtonStates(touchBits); // Update button states based on the touch data.
}

// Gets the current state of a specific button by its index.
// Returns true if pressed, false otherwise.
bool Matrix_getButtonState(uint8_t idx) {
    if (idx >= MATRIX_BUTTON_COUNT) // Bounds checking.
        return false;
    return buttonState[idx];
}

// Sets the function to be called when any button event (pressed or released) occurs.
// Tip: Consider using an observer pattern or a more flexible event system for complex applications.
void Matrix_setEventHandler(void (*handler)(const MatrixButtonEvent &)) {
    eventHandler = handler;
}

// Sets the function to be called when a button is pressed (rising edge).
// Tip: This provides a convenient way to handle press-only events.
void Matrix_setRisingEdgeHandler(void (*handler)(uint8_t buttonIndex)) {
    risingEdgeHandler = handler;
}

// Prints the current state of the entire button matrix to the Serial console for debugging.
// Tip: This is useful for debugging but should be disabled in production code to avoid performance overhead.
void Matrix_printState() {
    Serial.println("Button Matrix State (1=pressed, 0=not pressed):");
    for (uint8_t row = 0; row < TOUCH_MATRIX_ROWS; ++row) {
        for (uint8_t col = 0; col < TOUCH_MATRIX_COLS; ++col) {
            uint8_t idx = row * TOUCH_MATRIX_COLS + col;
            Serial.print(buttonState[idx] ? "1 " : "0 ");
        }
        Serial.println(); // Newline after each row.
    }
    Serial.println(); // Extra newline for readability.
}
