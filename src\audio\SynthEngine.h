#ifndef SYNTH_ENGINE_H
#define SYNTH_ENGINE_H

#include "../sequencer/SequencerDefs.h"
#include "../dsp/oscillator.h"
#include "../dsp/adsr.h"
#include "../dsp/svf.h"
#include "../dsp/ladder.h"
#include "../dsp/delayline.h"
#include "audio.h"

/**
 * @brief SynthEngine class encapsulates all audio synthesis logic
 * 
 * This class owns all DaisySP objects (oscillators, envelopes, filters, delay)
 * and handles the complete audio processing pipeline. It isolates complex
 * audio synthesis from sequencing and UI logic.
 */
class SynthEngine {
public:
    SynthEngine();
    ~SynthEngine();
    
    // Initialization
    void initialize(float sampleRate);
    
    // Main audio processing method
    void processAudio(audio_buffer_t *buffer);
    
    // Voice state updates from sequencer
    void updateVoiceState(const VoiceState &voice1, const VoiceState &voice2);
    void updateGlobalParams(const GlobalParams &params);
    
    // Gate control
    void setGate(int voiceIndex, bool gateState);
    
    // Utility methods
    float calculateFilterFrequency(float filterValue);
    void applyEnvelopeParameters(const VoiceState &state, daisysp::Adsr &env, int voiceNum);
    float delayTimeSmoothing(float currentDelay, float targetDelay, float slewRate);
    
private:
    // Audio synthesis components
    daisysp::Oscillator osc1A_, osc1B_, osc1C_, osc2A_, osc2B_, osc2C_, lfo1_, lfo2_;
    daisysp::Svf highPass1_, highPass2_, delLowPass_;
    daisysp::Adsr env1_, env2_;
    daisysp::DelayLine<float, static_cast<size_t>(48000.0f * 1.8f)> del1_;
    daisysp::LadderFilter filt1_, filt2_;
    
    // Voice state
    VoiceState voiceState1_;
    VoiceState voiceState2_;
    GlobalParams globalParams_;
    
    // Gate states
    volatile bool gate1_ = false;
    volatile bool gate2_ = false;
    
    // Frequency slewing for slide functionality
    struct SlewParams {
        float currentFreq = 440.0f;
        float targetFreq = 440.0f;
    };
    SlewParams freqSlew_[2][3];  // Two voices, three slots each
    
    // Audio parameters
    float feedbackGain1_ = 0.65f;
    float currentDelayOutputGain_ = 0.0f;
    float currentFeedbackGain_ = 0.0f;
    float delayTarget_ = 48000.0f * 0.15f;
    float currentDelay_ = 48000.0f * 0.15f;
    float feedbackAmount_ = 0.765f;
    float lfo1LEDWaveformValue_ = 0.0f;
    float lfo2LEDWaveformValue_ = 0.0f;
    
    // Audio constants
    float sampleRate_ = 48000.0f;
    static constexpr float FREQ_SLEW_RATE = 0.002f;
    static constexpr float FEEDBACK_FADE_RATE = 0.0001f;
    static constexpr float OSC_DETUNE_FACTOR = 1.007f;
    static constexpr float INT16_MAX_AS_FLOAT = 32767.0f;
    static constexpr float INT16_MIN_AS_FLOAT = -32768.0f;
    
    // Scale definitions
    static const int scale_[7][48];
    float baseFreq_ = 110.0f;
    float filterfreq1_ = 2000.0f;
    float filterfreq2_ = 2000.0f;
    uint8_t currentScale_ = 0;
    
    // Private helper methods
    void initializeOscillators();
    void processAudioEffects(float &sample1, float &sample2);
    void updateFrequencySlewing();
    float noteToFrequency(float note, float octave);
    void processVoice(int voiceIndex, float *leftSample, float *rightSample);
};

#endif // SYNTH_ENGINE_H