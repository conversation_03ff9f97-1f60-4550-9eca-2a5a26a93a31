# PicoMudras Polymetric Sequencer Visualizer

An interactive web application that visualizes the polymetric sequencing functionality of the PicoMudrasSequencer project.

## Features

### Visual Representation
- **Independent Parameter Tracks**: Displays all 9 parameter tracks (Note, Velocity, Filter, Attack, Decay, Octave, Gate Length, Gate, Slide) as separate visual sequences
- **Polymetric Visualization**: Shows how different parameter tracks advance independently with configurable step counts
- **Real-time Step Advancement**: Simulates the sequencer's step advancement with visual indicators
- **Pattern Analysis**: Displays polyrhythmic relationships and full cycle calculations

### Interactive Controls
- **Transport Controls**: Play, Stop, and Reset buttons
- **Tempo Control**: Adjustable tempo from 60-200 BPM
- **Parameter Track Lengths**: Individual sliders for each parameter track (1-16 steps)
- **Pattern Presets**: Pre-configured polymetric patterns (Basic 4/4, Polyrhythm, Complex, Random)

### Keyboard Shortcuts
- `Space`: Play/Pause
- `R`: Reset sequencer
- `1-4`: Load preset patterns

## Technology Stack

- **p5.js**: Graphics and animation library
- **HTML5/CSS3**: Modern web interface
- **Vanilla JavaScript**: Core sequencer logic

## How to Run

### Option 1: Python HTTP Server
```bash
cd SeqWebApp
python -m http.server 8080
```
Then open http://localhost:8080 in your browser.

### Option 2: Node.js HTTP Server
```bash
cd SeqWebApp
npx http-server -p 8080
```

### Option 3: Live Server (VS Code Extension)
If using VS Code, install the "Live Server" extension and right-click on `index.html` → "Open with Live Server".

### Option 4: Direct File Access
You can also open `index.html` directly in your browser, though some features may be limited due to CORS restrictions.

## Understanding Polymetric Sequencing

The PicoMudrasSequencer uses **polymetric sequencing**, where each parameter operates as an independent track with its own step count. This creates evolving patterns that repeat only after reaching the least common multiple (LCM) of all track lengths.

### Example:
- Note track: 16 steps
- Velocity track: 12 steps
- Filter track: 8 steps

The complete pattern repeats every 48 steps (LCM of 16, 12, and 8), creating complex evolving sequences.

## File Structure

```
SeqWebApp/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── sequencer.js        # Core sequencer logic and p5.js visualization
└── README.md          # This file
```

## Code Architecture

The web application mirrors the C++ PicoMudrasSequencer architecture:

### Core Components
1. **Parameter Tracks**: Array of track objects with individual step counts
2. **Sequencer State**: Global step counter and per-parameter step positions
3. **Visualization Engine**: p5.js-based real-time graphics
4. **UI Controls**: Interactive elements for parameter adjustment

### Key Functions
- `advanceStep()`: Core sequencer logic matching the C++ implementation
- `calculatePatternLength()`: LCM calculation for full pattern cycles
- `drawParameterTracks()`: Visual representation of all parameter tracks
- `loadPreset()`: Predefined polymetric patterns

## Customization

You can modify the sequencer behavior by editing `sequencer.js`:

- **Add new parameters**: Extend `PARAM_NAMES` and `PARAM_COLORS` arrays
- **Change step limits**: Modify `MAX_STEPS` and `MIN_STEPS` constants
- **Create new presets**: Add cases to the `loadPreset()` function
- **Adjust visualization**: Modify the `draw()` and related functions

## Browser Compatibility

- Chrome/Chromium: Full support
- Firefox: Full support
- Safari: Full support
- Edge: Full support

Requires a modern browser with ES6+ support.

## Related Projects

- [PicoMudrasSequencer](../): The main hardware sequencer project
- [p5.js](https://p5js.org/): Graphics library used for visualization

## License

This project follows the same license as the main PicoMudrasSequencer project.