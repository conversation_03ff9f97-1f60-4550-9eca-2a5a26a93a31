# Hardware Configuration System

This document describes the centralized hardware configuration system implemented in `HardwareConfig.h`.

## Overview

The `HardwareConfig.h` file centralizes all hardware pin definitions, buffer sizes, sample rates, and other static configuration values used throughout the PicoMudras Sequencer project. This approach provides several benefits:

- **Maintainability**: All hardware-specific values are in one location
- **Portability**: Easy to adapt the code for different hardware configurations
- **Consistency**: Prevents duplicate or conflicting definitions
- **Documentation**: Clear overview of all hardware requirements

## Configuration Categories

### Audio Configuration
- Sample rate and buffer sizes
- I2S pin assignments
- Audio processing constants
- Delay line parameters

---

## Coding and Functionality Rules

This project follows a defined set of coding and functionality rules to ensure code quality and maintainability. See `.roo/rules/rules.md` for full details.
### LED Matrix Configuration
- Matrix dimensions (8x8)
- Data pin assignment
- Animation parameters
- Brightness settings

### Touch Interface Configuration
- Matrix button layout (4x8 = 32 buttons)
- MPR121 pin mappings
- Interrupt pin assignments

### Sensor Configuration
- Distance sensor (VL53L1X) settings
- AS5600 magnetic encoder settings
- I2C pin assignments
- Update intervals

### Sequencer Configuration
- Timing parameters (PPQN)
- Number of steps and voices
- Shuffle template settings

### MIDI Configuration
- Channel assignments
- Note limits
- Default velocity

## Usage

To use the centralized configuration:

1. Include `HardwareConfig.h` in your module (or include `includes.h` which includes it)
2. Use the defined constants instead of hardcoded values
3. Update `HardwareConfig.h` when porting to new hardware

## Example

**Before (hardcoded values):**
```cpp
static constexpr uint8_t WIDTH = 8;
static constexpr uint8_t HEIGHT = 8;
static constexpr uint8_t DATA_PIN = 1;
```

**After (centralized configuration):**
```cpp
#include "../HardwareConfig.h"

static constexpr uint8_t WIDTH = LED_MATRIX_WIDTH;
static constexpr uint8_t HEIGHT = LED_MATRIX_HEIGHT;
static constexpr uint8_t DATA_PIN = LED_MATRIX_DATA_PIN;
```

## Hardware Feature Flags

The configuration includes feature flags that can be used to enable/disable hardware components:

- `FEATURE_TOUCH_BUTTONS_ENABLED`
- `FEATURE_TOUCH_MATRIX_ENABLED`
- `FEATURE_DISTANCE_SENSOR_ENABLED`
- `FEATURE_AS5600_ENCODER_ENABLED`
- `FEATURE_LED_MATRIX_ENABLED`
- `FEATURE_USB_MIDI_ENABLED`

## Validation

The configuration file includes compile-time validation macros to catch configuration errors early:

```cpp
#if AUDIO_SAMPLE_RATE <= 0
#error "AUDIO_SAMPLE_RATE must be positive"
#endif
```

## Pin Assignment Summary

| Component | Pin(s) | Description |
|-----------|--------|--------------|
| LED Matrix | GPIO 1 | WS2812B data |
| I2S Audio | GPIO 15, 16-17 | Data, Clock |
| I2C Bus | GPIO 4-5 | SDA, SCL |
| Touch IRQ | GPIO 10 | MPR121 interrupt |
| Distance Sensor IRQ | GPIO 2 | VL53L1X interrupt (optional) |

## Porting to New Hardware

To port the code to different hardware:

1. Update pin assignments in `HardwareConfig.h`
2. Modify buffer sizes if needed for different memory constraints
3. Adjust timing constants for different clock speeds
4. Update feature flags to match available hardware
5. Recompile - validation macros will catch any conflicts

## Files Updated

The following files have been updated to use the centralized configuration:

- `src/PicoMudrasApp.h` - Main application constants
- `src/LEDMatrix/ledMatrix.h` - LED matrix dimensions and pins
- `src/LEDMatrix/LEDController.cpp` - Animation constants
- `src/matrix/Matrix.h` - Touch matrix configuration
- `src/matrix/Matrix.cpp` - Pin mappings and dimensions
- `src/sensors/DistanceSensor.h` - Sensor timing
- `src/sequencer/ShuffleTemplates.h` - Template counts
- `src/sequencer/ParameterManager.cpp` - PPQN settings

## Future Enhancements

Possible future improvements:

- Runtime configuration loading from EEPROM/flash
- Multiple hardware profile support
- Automatic pin conflict detection
- Configuration validation tools